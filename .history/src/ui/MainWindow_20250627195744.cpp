#include "MainWindow.h"
#include "TimelineWidget.h"
#include "../core/AAFReader.h"
#include "../utils/TimecodeUtils.h"
#include "../core/MemoryManager.h"
#include "../python/PythonBridge.h"
#include "../audio/AudioAnalyzer.h"
#include "../audio/AudioFileManager.h"
#include "../core/ProgressTracker.h"
#include "../core/AnalysisSettings.h"
#include "../audio/ClassificationEngine.h"
#include "../core/TrackOrganizer.h"
#include "../ai/LMStudioClient.h"
#include "ClassificationReviewDialog.h"
#include "PresetManagementDialog.h"
#include "../export/AAFExporter.h"
#include <QApplication>
#include <QMenuBar>
#include <QStatusBar>
#include <QAction>
#include <QCloseEvent>
#include <QDateTime>
#include <QMessageBox>
#include <QRadioButton>
#include <QFileDialog>
#include <QFormLayout>
#include <QDialog>
#include <QLineEdit>
#include <QStandardPaths>
#include <QNetworkAccessManager>
#include <QNetworkRequest>
#include <QNetworkReply>
#include <QUrl>
#include <QRegularExpression>
#include <functional>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_aafReader(nullptr)
    , m_memoryManager(nullptr)
    , m_pythonBridge(nullptr)
    , m_audioAnalyzer(nullptr)
    , m_audioFileManager(nullptr)
    , m_progressTracker(nullptr)
    , m_analysisSettings(nullptr)
    , m_classificationEngine(nullptr)
    , m_trackOrganizer(nullptr)
    , m_lmStudioClient(nullptr)
    , m_reviewDialog(nullptr)
    , m_timelineWidget(nullptr)
    , m_centralWidget(nullptr)
    , m_statusTimer(new QTimer(this))
    , m_networkManager(new QNetworkAccessManager(this))
{
    setWindowTitle("WAAFer - AI-Powered AAF Audio Organizer");
    setMinimumSize(1200, 800);

    // macOS-specific window configuration for maximum visibility
#ifdef Q_OS_MACOS
    // Ensure window appears on screen and is properly configured
    setWindowFlags(Qt::Window | Qt::WindowTitleHint | Qt::WindowSystemMenuHint |
                   Qt::WindowMinMaxButtonsHint | Qt::WindowCloseButtonHint);
    setAttribute(Qt::WA_ShowWithoutActivating, false);
    setAttribute(Qt::WA_NativeWindow, true);
    // setAttribute(Qt::WA_MacFrameworkScaled, true); // Not available in this Qt version
    setAttribute(Qt::WA_DontCreateNativeAncestors, false);

    // Force window to be visible and interactive
    setWindowModality(Qt::NonModal);
    setFocusPolicy(Qt::StrongFocus);
#endif

    // Load settings
    qDebug() << "=== MainWindow: Loading settings ===";
    loadSettings();
    qDebug() << "MainWindow: Settings loaded successfully";

    // Initialize UI immediately instead of using delayed initialization
    qDebug() << "=== MainWindow: Starting immediate UI initialization ===";
    qDebug() << "MainWindow: Window geometry before setupUI:" << geometry();
    qDebug() << "MainWindow: Window size before setupUI:" << size();

    qDebug() << "MainWindow: Starting setupUI()";
    setupUI();
    qDebug() << "MainWindow: setupUI() completed successfully";
    qDebug() << "MainWindow: Central widget after setupUI:" << centralWidget();
    qDebug() << "MainWindow: Window size after setupUI:" << size();

    qDebug() << "MainWindow: Setting up menu bar";
    setupMenuBar();
    qDebug() << "MainWindow: Menu bar setup completed";
    qDebug() << "MainWindow: Menu bar visible:" << menuBar()->isVisible();

    qDebug() << "MainWindow: Setting up status bar";
    setupStatusBar();
    qDebug() << "MainWindow: Status bar setup completed";
    qDebug() << "MainWindow: Status bar visible:" << statusBar()->isVisible();

    // Setup status update timer
    qDebug() << "MainWindow: Setting up status timer";
    connect(m_statusTimer, &QTimer::timeout, this, &MainWindow::updateStatus);
    m_statusTimer->start(2000); // Update every 2 seconds
    qDebug() << "MainWindow: Status timer started";

    qDebug() << "=== MainWindow: Checking widget hierarchy after setup ===";
    qDebug() << "MainWindow: Central widget:" << centralWidget();
    qDebug() << "MainWindow: Widget size:" << size();
    qDebug() << "MainWindow: Widget minimum size:" << minimumSize();
    qDebug() << "MainWindow: Widget is visible:" << isVisible();
    qDebug() << "MainWindow: Widget window flags:" << windowFlags();
    qDebug() << "MainWindow: Widget has focus:" << hasFocus();
    qDebug() << "MainWindow: Widget is enabled:" << isEnabled();
    qDebug() << "MainWindow: Widget window state:" << windowState();

    qDebug() << "=== MainWindow: UI initialization completed successfully ===";

    // Force window to be visible and on top
    qDebug() << "=== MainWindow: Forcing window visibility in constructor ===";
    qDebug() << "MainWindow: Before show() - visible:" << isVisible() << "geometry:" << geometry();
    show();
    qDebug() << "MainWindow: After show() - visible:" << isVisible() << "geometry:" << geometry();

    raise();
    qDebug() << "MainWindow: After raise() - visible:" << isVisible() << "active:" << isActiveWindow();

    activateWindow();
    qDebug() << "MainWindow: After activateWindow() - visible:" << isVisible() << "active:" << isActiveWindow();

    // Force immediate repaint
    repaint();
    qDebug() << "MainWindow: After repaint() - visible:" << isVisible();

    qDebug() << "=== MainWindow: Constructor completed successfully ===";
}

void MainWindow::forceWindowVisible()
{
    qDebug() << "MainWindow::forceWindowVisible: Implementing comprehensive visibility strategy";

    // Strategy 1: Basic show operations
    if (!isVisible()) {
        qDebug() << "Window not visible, calling show()";
        show();
    }

    // Strategy 2: Window state manipulation
    qDebug() << "Setting window state to normal";
    setWindowState(Qt::WindowNoState);
    showNormal();

    // Strategy 3: Bring to front operations
    qDebug() << "Bringing window to front";
    raise();
    activateWindow();

    // Strategy 4: macOS specific activation
#ifdef Q_OS_MACOS
    qDebug() << "Applying macOS-specific activation";
    setWindowState(Qt::WindowActive);

    // Force window to front using macOS-specific methods
    setAttribute(Qt::WA_MacAlwaysShowToolWindow, true);
    setAttribute(Qt::WA_MacAlwaysShowToolWindow, false);

    // Temporarily set stay on top to force visibility
    Qt::WindowFlags originalFlags = windowFlags();
    setWindowFlags(originalFlags | Qt::WindowStaysOnTopHint);
    show();
    setWindowFlags(originalFlags);
    show();
#endif

    // Strategy 5: Force repaint and update
    qDebug() << "Forcing repaint and update";
    repaint();
    update();

    // Strategy 6: Process events to ensure visibility
    QApplication::processEvents(QEventLoop::AllEvents, 100);

    qDebug() << "MainWindow::forceWindowVisible: Final state - isVisible:" << isVisible()
             << "isActiveWindow:" << isActiveWindow() << "geometry:" << geometry()
             << "windowState:" << windowState();
}

MainWindow::~MainWindow()
{
    // Save settings before closing
    saveSettings();
    qDebug() << "MainWindow destroyed";
}

QWidget* MainWindow::createTestWindow()
{
    qDebug() << "Creating simple test window for GUI debugging";

    QWidget *testWindow = new QWidget();
    testWindow->setWindowTitle("WAAFer Test Window - GUI Visibility Check");
    testWindow->resize(400, 300);

    // Center on screen
    QRect screenGeometry = QApplication::primaryScreen()->availableGeometry();
    int x = screenGeometry.x() + (screenGeometry.width() - 400) / 2;
    int y = screenGeometry.y() + (screenGeometry.height() - 300) / 2;
    testWindow->move(x, y);

    // Create simple layout with visible elements
    QVBoxLayout *layout = new QVBoxLayout(testWindow);

    QLabel *titleLabel = new QLabel("WAAFer GUI Visibility Test");
    titleLabel->setAlignment(Qt::AlignCenter);
    titleLabel->setStyleSheet("font-size: 18px; font-weight: bold; color: #2E8B57; padding: 20px;");

    QLabel *statusLabel = new QLabel("If you can see this window, Qt GUI is working correctly.");
    statusLabel->setAlignment(Qt::AlignCenter);
    statusLabel->setWordWrap(true);
    statusLabel->setStyleSheet("font-size: 14px; padding: 10px;");

    QPushButton *closeButton = new QPushButton("Close Test Window");
    closeButton->setStyleSheet("QPushButton { padding: 10px; font-size: 14px; }");

    layout->addWidget(titleLabel);
    layout->addWidget(statusLabel);
    layout->addStretch();
    layout->addWidget(closeButton);

    // Connect close button
    QObject::connect(closeButton, &QPushButton::clicked, testWindow, &QWidget::close);

    // Set window attributes for maximum visibility
    testWindow->setAttribute(Qt::WA_ShowWithoutActivating, false);
    testWindow->setAttribute(Qt::WA_QuitOnClose, false);

#ifdef Q_OS_MACOS
    testWindow->setWindowFlags(Qt::Window | Qt::WindowTitleHint | Qt::WindowCloseButtonHint);
    testWindow->setAttribute(Qt::WA_NativeWindow, true);
#endif

    qDebug() << "Test window created successfully";
    return testWindow;
}

void MainWindow::setCoreComponents(AAFReader *aafReader,
                                  MemoryManager *memoryManager,
                                  PythonBridge *pythonBridge,
                                  LibAAFWrapper *libAAFWrapper,
                                  AudioAnalyzer *audioAnalyzer,
                                  AudioFileManager *audioFileManager,
                                  ProgressTracker *progressTracker,
                                  AnalysisSettings *analysisSettings,
                                  ClassificationEngine *classificationEngine,
                                  TrackOrganizer *trackOrganizer,
                                  LMStudioClient *lmStudioClient,
                                  AudioPlaybackManager *audioPlaybackManager,
                                  AAFExporter *aafExporter)
{
    m_aafReader = aafReader;
    m_memoryManager = memoryManager;
    m_pythonBridge = pythonBridge;
    m_libAAFWrapper = libAAFWrapper;
    m_audioAnalyzer = audioAnalyzer;
    m_audioFileManager = audioFileManager;
    m_progressTracker = progressTracker;

    // Connect memory manager to AAF reader
    if (m_aafReader && m_memoryManager) {
        m_aafReader->setMemoryManager(m_memoryManager);
    }
    m_analysisSettings = analysisSettings;
    m_classificationEngine = classificationEngine;
    m_trackOrganizer = trackOrganizer;
    m_lmStudioClient = lmStudioClient;
    m_audioPlaybackManager = audioPlaybackManager;
    m_aafExporter = aafExporter;

    // Handle Phase 4 components
    if (!m_lmStudioClient) {
        qDebug() << "Phase 4 components disabled - running without AI assistance";
    } else {
        qDebug() << "Phase 4 AI components enabled";
    }

    // Connect components to LibAAF wrapper and Python bridge
    if (m_aafReader && m_libAAFWrapper) {
        m_aafReader->setLibAAFWrapper(m_libAAFWrapper);
    }

    if (m_aafReader && m_pythonBridge) {
        m_aafReader->setPythonBridge(m_pythonBridge);
    }

    if (m_audioAnalyzer && m_pythonBridge) {
        m_audioAnalyzer->setPythonBridge(m_pythonBridge);
    }

    if (m_audioFileManager && m_pythonBridge) {
        m_audioFileManager->setPythonBridge(m_pythonBridge);
    }

    // Connect Phase 3 components
    if (m_classificationEngine && m_pythonBridge) {
        m_classificationEngine->setPythonBridge(m_pythonBridge);
    }

    if (m_classificationEngine && m_analysisSettings) {
        m_classificationEngine->setAnalysisSettings(m_analysisSettings);
    }

    if (m_classificationEngine && m_progressTracker) {
        m_classificationEngine->setProgressTracker(m_progressTracker);
    }

    if (m_classificationEngine && m_audioFileManager) {
        m_classificationEngine->setAudioFileManager(m_audioFileManager);
    }
    
    // Initialize Phase 5 components
    if (m_timelineWidget) {
        qDebug() << "TimelineWidget::setAAFReader: Starting";
        m_timelineWidget->setAAFReader(m_aafReader);
        qDebug() << "TimelineWidget::setAAFReader: Completed";

        qDebug() << "TimelineWidget::setAudioFileManager: Starting";
        m_timelineWidget->setAudioFileManager(m_audioFileManager);
        qDebug() << "TimelineWidget::setAudioFileManager: Completed";

        if (m_audioPlaybackManager) {
            qDebug() << "TimelineWidget::setAudioPlaybackManager: Starting";
            m_timelineWidget->setAudioPlaybackManager(m_audioPlaybackManager);
            qDebug() << "TimelineWidget::setAudioPlaybackManager: Completed";
        }
    }

    // Connect LM Studio client to TrackOrganizer
    if (m_trackOrganizer && m_lmStudioClient) {
        m_trackOrganizer->setLMStudioClient(m_lmStudioClient);
    }

    // Initialize AAF Exporter
    if (m_aafExporter) {
        m_aafExporter->setPythonBridge(m_pythonBridge);
        m_aafExporter->setAAFReader(m_aafReader);
        m_aafExporter->setLibAAFWrapper(m_libAAFWrapper);
        qDebug() << "AAF Exporter configured with LibAAF wrapper for consistent AAF handling";
    }

    setupConnections();
    updateStatus();

    // Configure LM Studio client with saved settings (after UI is set up)
    if (m_lmStudioClient) {
        QUrl serverUrl(m_llmSettingsData.lmStudioUrl);
        m_lmStudioClient->setServerUrl(serverUrl);
        addLogMessage(QString("LM Studio client configured for: %1").arg(m_llmSettingsData.lmStudioUrl));
    }

    addLogMessage("Core components connected successfully");
}

void MainWindow::closeEvent(QCloseEvent *event)
{
    addLogMessage("Application closing...");
    
    // Close any open files
    if (m_aafReader && m_aafReader->isLoaded()) {
        m_aafReader->closeFile();
    }
    
    // Clean up Python bridge
    if (m_pythonBridge) {
        m_pythonBridge->finalize();
    }
    
    event->accept();
}

void MainWindow::openAAFFile()
{
    QString fileName = QFileDialog::getOpenFileName(
        this,
        "Open AAF File",
        QString(),
        "AAF Files (*.aaf *.AAF);;All Files (*)"
    );

    if (!fileName.isEmpty() && m_aafReader) {
        addLogMessage(QString("Opening AAF file: %1").arg(fileName));
        m_progressBar->setVisible(true);
        m_progressBar->setValue(0);

        m_aafReader->loadFile(fileName);
    }
}

void MainWindow::loadAAFFile(const QString &filePath)
{
    qDebug() << "MainWindow::loadAAFFile called with:" << filePath;

    if (filePath.isEmpty()) {
        qWarning() << "Empty file path provided to loadAAFFile";
        return;
    }

    if (!QFile::exists(filePath)) {
        qWarning() << "File does not exist:" << filePath;
        addLogMessage(QString("Error: File does not exist: %1").arg(filePath), true);
        return;
    }

    if (!m_aafReader) {
        qWarning() << "AAFReader not available";
        addLogMessage("Error: AAF Reader not initialized", true);
        return;
    }

    qDebug() << "Loading AAF file:" << filePath;
    addLogMessage(QString("Loading AAF file: %1").arg(filePath));
    m_progressBar->setVisible(true);
    m_progressBar->setValue(0);

    m_aafReader->loadFile(filePath);
}

void MainWindow::closeAAFFile()
{
    if (m_aafReader && m_aafReader->isLoaded()) {
        addLogMessage("Closing AAF file");
        m_aafReader->closeFile();
        m_progressBar->setVisible(false);
        updateFileInfo();
    }
}

void MainWindow::testPythonIntegration()
{
    if (!m_pythonBridge) {
        addLogMessage("Python bridge not available", true);
        return;
    }

    addLogMessage("Testing Python integration...");

    if (m_pythonBridge->testConnection()) {
        addLogMessage("Python integration test successful");

    } else {
        addLogMessage("Python integration test failed", true);
    }
}

void MainWindow::testAudioLibraries()
{
    if (!m_pythonBridge) {
        addLogMessage("Python bridge not available", true);
        return;
    }

    addLogMessage("Testing audio libraries...");

    bool audioOk = m_pythonBridge->testAudioLibraries();
    bool aafOk = m_pythonBridge->testAAFLibraries();
    bool aiOk = m_pythonBridge->testAILibraries();

    if (audioOk && aafOk && aiOk) {
        addLogMessage("All audio and AI libraries available");
    } else {
        QString message = "Library test results: ";
        message += QString("Audio: %1, ").arg(audioOk ? "OK" : "FAIL");
        message += QString("AAF: %1, ").arg(aafOk ? "OK" : "FAIL");
        message += QString("AI: %1").arg(aiOk ? "OK" : "FAIL");
        addLogMessage(message, !audioOk || !aafOk || !aiOk);
    }
}

void MainWindow::showMemoryStats()
{
    if (!m_memoryManager) {
        addLogMessage("Memory manager not available", true);
        return;
    }
    
    QVariantMap stats = m_memoryManager->getMemoryStats();
    
    QString message = "Memory Statistics:\n";
    message += QString("Total Usage: %1\n").arg(formatBytes(stats["totalUsage"].toLongLong()));
    message += QString("Max Limit: %1\n").arg(formatBytes(stats["maxLimit"].toLongLong()));
    message += QString("Usage: %1%\n").arg(stats["usagePercentage"].toDouble(), 0, 'f', 1);
    message += QString("Chunks: %1\n").arg(stats["chunkCount"].toInt());
    message += QString("Cache Hit Rate: %1%\n").arg(stats["cacheHitRate"].toInt());
    message += QString("Optimized: %1").arg(stats["isOptimized"].toBool() ? "Yes" : "No");
    
    QMessageBox::information(this, "Memory Statistics", message);
}

void MainWindow::updateStatus()
{
    QString status = "Ready";
    
    if (m_aafReader && m_aafReader->isLoaded()) {
        status = QString("File: %1").arg(m_aafReader->currentFile());
    }
    
    if (m_pythonBridge && m_pythonBridge->isInitialized()) {
        status += " | Python: Ready";
    } else {
        status += " | Python: Not Ready";
    }
    
    if (m_memoryManager) {
        QVariantMap stats = m_memoryManager->getMemoryStats();
        double usage = stats["usagePercentage"].toDouble();
        status += QString(" | Memory: %1%").arg(usage, 0, 'f', 1);
    }
    
    m_statusLabel->setText(status);
    
    // Update displays
    updateFileInfo();
    updateMemoryUsage();
}

void MainWindow::onAAFFileLoadCompleted(bool success, const QString &message)
{
    m_progressBar->setVisible(false);

    if (success) {
        addLogMessage(QString("AAF file loaded successfully: %1").arg(message));

        // Set the current AAF file path in the ClassificationEngine
        if (m_classificationEngine && m_aafReader) {
            QString currentAAFFile = m_aafReader->currentFile();
            m_classificationEngine->setCurrentAAFFile(currentAAFFile);
            qDebug() << "Set AAF file path in ClassificationEngine:" << currentAAFFile;
        }

        // Load timeline data from AAF file
        if (m_timelineWidget && m_aafReader) {
            QVariantList regions = m_aafReader->getAllRegions();
            QVariantList tracks; // Empty for auto-generation

            // Debug: Log first few regions to verify AAF data extraction
            addLogMessage(QString("AAF file loaded with %1 regions").arg(regions.size()));
            for (int i = 0; i < qMin(3, regions.size()); ++i) {
                QVariantMap region = regions[i].toMap();
                QString regionName = region.value("name").toString();
                QString trackName = region.value("track").toString();
                double startTime = region.value("startTime").toDouble();
                QString audioFilePath = region.value("audioFilePath").toString();
                addLogMessage(QString("Region %1: '%2' on track '%3' at %4s (audio: %5)")
                             .arg(i+1).arg(regionName).arg(trackName).arg(startTime).arg(audioFilePath));
            }

            m_timelineWidget->loadTimelineData(regions, tracks);

            // Force immediate refresh to ensure visibility
            QTimer::singleShot(100, [this]() {
                if (m_timelineWidget) {
                    m_timelineWidget->forceRefresh();
                }
            });

            addLogMessage("Timeline loaded successfully");
        }
    } else {
        addLogMessage(QString("Failed to load AAF file: %1").arg(message), true);
    }

    updateFileInfo();
}

void MainWindow::onAAFParsingProgress(int progress, const QString &status)
{
    m_progressBar->setValue(progress);
    addLogMessage(QString("Parsing: %1% - %2").arg(progress).arg(status));
}

void MainWindow::onPythonOperationCompleted(bool success, const QString &message)
{
    if (success) {
        addLogMessage(QString("Python operation completed: %1").arg(message));
    } else {
        addLogMessage(QString("Python operation failed: %1").arg(message), true);
    }
}

void MainWindow::onMemoryWarning(int usage, const QString &message)
{
    addLogMessage(QString("Memory warning (%1%): %2").arg(usage).arg(message), true);
    updateMemoryUsage();
}

void MainWindow::setupUI()
{
    qDebug() << "=== setupUI: Starting UI creation ===";
    qDebug() << "setupUI: Window size before UI creation:" << size();
    qDebug() << "setupUI: Window geometry before UI creation:" << geometry();

    qDebug() << "setupUI: Creating central widget";
    m_centralWidget = new QWidget;
    qDebug() << "setupUI: Central widget created:" << m_centralWidget;

    setCentralWidget(m_centralWidget);
    qDebug() << "setupUI: Central widget set successfully";
    qDebug() << "setupUI: Central widget size:" << m_centralWidget->size();
    qDebug() << "setupUI: Central widget visible:" << m_centralWidget->isVisible();

    // Create main layout
    qDebug() << "setupUI: Creating main layout";
    QVBoxLayout *mainLayout = new QVBoxLayout(m_centralWidget);
    qDebug() << "setupUI: Main layout created and set on central widget";

    // Add progress tracking at the top
    qDebug() << "setupUI: Creating progress tracking group";
    QGroupBox *progressGroup = createProgressTrackingGroup();
    qDebug() << "setupUI: Progress tracking group created:" << progressGroup;
    mainLayout->addWidget(progressGroup);
    qDebug() << "setupUI: Progress tracking group added to layout";

    // Create main splitter
    qDebug() << "setupUI: Creating main splitter";
    m_mainSplitter = new QSplitter(Qt::Horizontal);
    qDebug() << "setupUI: Main splitter created:" << m_mainSplitter;

    // Create left panel with basic controls
    qDebug() << "setupUI: Creating left panel";
    QWidget *leftPanel = new QWidget;
    leftPanel->setMaximumWidth(400);
    leftPanel->setMinimumWidth(350);
    qDebug() << "setupUI: Left panel created:" << leftPanel;

    QVBoxLayout *leftLayout = new QVBoxLayout(leftPanel);
    qDebug() << "setupUI: Left layout created";

    qDebug() << "setupUI: Creating file operations group";
    QGroupBox *fileOpsGroup = createFileOperationsGroup();
    qDebug() << "setupUI: File operations group created:" << fileOpsGroup;
    leftLayout->addWidget(fileOpsGroup);

    qDebug() << "setupUI: Creating classification progress group";
    QGroupBox *classProgressGroup = createClassificationProgressGroup();
    qDebug() << "setupUI: Classification progress group created:" << classProgressGroup;
    leftLayout->addWidget(classProgressGroup);

    qDebug() << "setupUI: Creating memory management group";
    QGroupBox *memoryGroup = createMemoryManagementGroup();
    qDebug() << "setupUI: Memory management group created:" << memoryGroup;
    leftLayout->addWidget(memoryGroup);

    leftLayout->addStretch();
    qDebug() << "setupUI: Left panel setup completed successfully";

    // Create tabbed interface for Phase 3-5 features
    qDebug() << "setupUI: Creating tab widget";
    m_tabWidget = new QTabWidget;
    qDebug() << "setupUI: Tab widget created:" << m_tabWidget;

    qDebug() << "setupUI: Creating Timeline tab";
    QWidget *timelineTab = createTimelineTab();
    qDebug() << "setupUI: Timeline tab created:" << timelineTab;
    m_tabWidget->addTab(timelineTab, "Timeline");
    qDebug() << "setupUI: Timeline tab added successfully";

    qDebug() << "setupUI: Creating Classification tab";
    QWidget *classificationTab = createClassificationTab();
    qDebug() << "setupUI: Classification tab created:" << classificationTab;
    m_tabWidget->addTab(classificationTab, "Classification");
    qDebug() << "setupUI: Classification tab added successfully";

    qDebug() << "setupUI: Creating Organization tab";
    QWidget *organizationTab = createTrackOrganizationTab();
    qDebug() << "setupUI: Organization tab created:" << organizationTab;
    m_tabWidget->addTab(organizationTab, "Organization");
    qDebug() << "setupUI: Organization tab added successfully";

    qDebug() << "setupUI: Creating Export tab";
    QWidget *exportTab = createExportTab();
    qDebug() << "setupUI: Export tab created:" << exportTab;
    m_tabWidget->addTab(exportTab, "Export");
    qDebug() << "setupUI: Export tab added successfully";

    qDebug() << "setupUI: Creating Log tab";
    QGroupBox *logTab = createLogOutputGroup();
    qDebug() << "setupUI: Log tab created:" << logTab;
    m_tabWidget->addTab(logTab, "Log");
    qDebug() << "setupUI: Log tab added successfully";
    qDebug() << "setupUI: All tabs created and added successfully";
    qDebug() << "setupUI: Tab widget has" << m_tabWidget->count() << "tabs";

    // Connect Timeline analysis range signals now that all tabs (including Classification) are created
    qDebug() << "setupUI: Connecting Timeline analysis range signals";
    connect(m_timelineWidget, &TimelineWidget::analysisRangeChanged,
            this, &MainWindow::onTimelineAnalysisRangeChanged);
    connect(m_timelineWidget, &TimelineWidget::setTimeRangeFromTimelineRequested,
            this, &MainWindow::setTimeRangeFromTimeline);
    connect(m_timelineWidget, &TimelineWidget::clearTimeRangeRequested,
            this, &MainWindow::clearTimeRange);
    qDebug() << "setupUI: Timeline analysis range signals connected successfully";

    // Add panels to splitter
    qDebug() << "setupUI: Adding panels to splitter";
    qDebug() << "setupUI: Left panel size:" << leftPanel->size();
    qDebug() << "setupUI: Tab widget size:" << m_tabWidget->size();

    m_mainSplitter->addWidget(leftPanel);
    qDebug() << "setupUI: Left panel added to splitter";

    m_mainSplitter->addWidget(m_tabWidget);
    qDebug() << "setupUI: Tab widget added to splitter";

    m_mainSplitter->setStretchFactor(0, 0);
    m_mainSplitter->setStretchFactor(1, 1);
    qDebug() << "setupUI: Splitter stretch factors set";

    // Add splitter to main layout
    qDebug() << "setupUI: Adding splitter to main layout";
    mainLayout->addWidget(m_mainSplitter);
    qDebug() << "setupUI: Splitter added to main layout successfully";

    qDebug() << "setupUI: Final central widget size:" << m_centralWidget->size();
    qDebug() << "setupUI: Final window size:" << size();
    qDebug() << "=== setupUI: UI creation completed successfully ===";
}

void MainWindow::setupMenuBar()
{
    QMenuBar *menuBar = this->menuBar();
    
    // File menu
    QMenu *fileMenu = menuBar->addMenu("&File");
    
    QAction *openAction = fileMenu->addAction("&Open AAF File...");
    openAction->setShortcut(QKeySequence::Open);
    connect(openAction, &QAction::triggered, this, &MainWindow::openAAFFile);
    
    QAction *closeAction = fileMenu->addAction("&Close File");
    closeAction->setShortcut(QKeySequence::Close);
    connect(closeAction, &QAction::triggered, this, &MainWindow::closeAAFFile);
    
    fileMenu->addSeparator();
    
    QAction *exitAction = fileMenu->addAction("E&xit");
    exitAction->setShortcut(QKeySequence::Quit);
    connect(exitAction, &QAction::triggered, this, &QWidget::close);
    
    // Tools menu
    QMenu *toolsMenu = menuBar->addMenu("&Tools");
    
    QAction *testPythonAction = toolsMenu->addAction("Test &Python Integration");
    connect(testPythonAction, &QAction::triggered, this, &MainWindow::testPythonIntegration);
    
    QAction *testAudioAction = toolsMenu->addAction("Test &Audio Libraries");
    connect(testAudioAction, &QAction::triggered, this, &MainWindow::testAudioLibraries);
    
    QAction *memoryStatsAction = toolsMenu->addAction("&Memory Statistics");
    connect(memoryStatsAction, &QAction::triggered, this, &MainWindow::showMemoryStats);

    toolsMenu->addSeparator();

    QAction *llmSettingsAction = toolsMenu->addAction("&LLM Settings");
    connect(llmSettingsAction, &QAction::triggered, this, &MainWindow::openLLMSettings);

    // Help menu
    QMenu *helpMenu = menuBar->addMenu("&Help");
    
    QAction *aboutAction = helpMenu->addAction("&About WAAFer");
    connect(aboutAction, &QAction::triggered, [this]() {
        QMessageBox::about(this, "About WAAFer", 
            "WAAFer - AI-Powered AAF Audio Organizer\n\n"
            "Version 1.0.0\n"
            "Built with Qt6 and Python integration\n\n"
            "Automatically classifies and organizes audio regions\n"
            "in AAF files using AI and metadata analysis.");
    });
}

void MainWindow::setupStatusBar()
{
    m_statusLabel = new QLabel("Initializing...");
    statusBar()->addWidget(m_statusLabel);
}

void MainWindow::setupConnections()
{
    if (m_aafReader) {
        connect(m_aafReader, &AAFReader::fileLoadCompleted,
                this, &MainWindow::onAAFFileLoadCompleted);
        connect(m_aafReader, &AAFReader::parsingProgress,
                this, &MainWindow::onAAFParsingProgress);
        connect(m_aafReader, &AAFReader::durationChanged,
                this, &MainWindow::updateFileInfo);
        connect(m_aafReader, &AAFReader::frameRateChanged,
                this, &MainWindow::updateFileInfo);
        connect(m_aafReader, &AAFReader::timecodeFormatChanged,
                this, &MainWindow::updateFileInfo);
    }

    if (m_pythonBridge) {
        connect(m_pythonBridge, &PythonBridge::pythonOperationCompleted,
                this, &MainWindow::onPythonOperationCompleted);
    }

    if (m_memoryManager) {
        connect(m_memoryManager, &MemoryManager::memoryWarning,
                this, &MainWindow::onMemoryWarning);
        connect(m_memoryManager, &MemoryManager::memoryUsageChanged,
                this, &MainWindow::updateMemoryUsage);
    }

    // Phase 3 connections
    if (m_progressTracker) {
        connect(m_progressTracker, &ProgressTracker::progressChanged,
                this, &MainWindow::onProgressChanged);
        connect(m_progressTracker, &ProgressTracker::currentTaskChanged,
                this, &MainWindow::onProgressTaskChanged);
        connect(m_progressTracker, &ProgressTracker::timeRemainingChanged,
                this, &MainWindow::onProgressTimeChanged);
        connect(m_progressTracker, &ProgressTracker::elapsedTimeChanged,
                this, &MainWindow::onProgressTimeChanged);
    }

    if (m_classificationEngine) {
        connect(m_classificationEngine, &ClassificationEngine::classificationCompleted,
                this, &MainWindow::onClassificationCompleted);
        connect(m_classificationEngine, &ClassificationEngine::classificationError,
                this, &MainWindow::onClassificationError);
        connect(m_classificationEngine, &ClassificationEngine::overallProgressChanged,
                this, &MainWindow::onClassificationProgressChanged);
    }

    if (m_trackOrganizer) {
        connect(m_trackOrganizer, &TrackOrganizer::organizationCompleted,
                this, &MainWindow::onOrganizationCompleted);
    }

    // Phase 5 connections
    if (m_timelineWidget) {
        connect(m_timelineWidget, &TimelineWidget::regionSelected,
                this, &MainWindow::onTimelineRegionSelected);
        connect(m_timelineWidget, &TimelineWidget::regionUpdated,
                this, &MainWindow::onTimelineRegionUpdated);
        connect(m_timelineWidget, &TimelineWidget::playbackRequested,
                this, &MainWindow::onTimelinePlaybackRequested);
        connect(m_timelineWidget, &TimelineWidget::timeRangeSelectionChanged,
                this, &MainWindow::onTimelineRangeSelected);
    }
}

QGroupBox* MainWindow::createFileOperationsGroup()
{
    m_fileGroup = new QGroupBox("File Operations");
    QVBoxLayout *layout = new QVBoxLayout(m_fileGroup);

    // File buttons
    m_openFileButton = new QPushButton("Open AAF File...");
    m_closeFileButton = new QPushButton("Close File");
    m_closeFileButton->setEnabled(false);

    connect(m_openFileButton, &QPushButton::clicked, this, &MainWindow::openAAFFile);
    connect(m_closeFileButton, &QPushButton::clicked, this, &MainWindow::closeAAFFile);

    // File info
    m_fileInfoLabel = new QLabel("No file loaded");
    m_fileInfoLabel->setWordWrap(true);

    // Timecode display
    m_timecodeLabel = new QLabel("Duration: --:--:--.---");
    m_timecodeLabel->setStyleSheet("QLabel { font-family: 'Monaco', 'Menlo', 'Consolas', monospace; font-weight: bold; color: #2E8B57; }");

    // Progress bar
    m_progressBar = new QProgressBar;
    m_progressBar->setVisible(false);

    layout->addWidget(m_openFileButton);
    layout->addWidget(m_closeFileButton);
    layout->addWidget(m_fileInfoLabel);
    layout->addWidget(m_timecodeLabel);
    layout->addWidget(m_progressBar);

    return m_fileGroup;
}

QGroupBox* MainWindow::createClassificationProgressGroup()
{
    m_analysisProgressGroup = new QGroupBox("Classification Analysis");
    QVBoxLayout *layout = new QVBoxLayout(m_analysisProgressGroup);

    // Analysis status
    m_analysisStatusLabel = new QLabel("Ready to analyze audio regions");
    m_analysisStatusLabel->setStyleSheet("QLabel { color: #333; font-weight: bold; }");

    // Analysis progress bar
    m_analysisProgressBar = new QProgressBar;
    m_analysisProgressBar->setVisible(false);
    m_analysisProgressBar->setStyleSheet(
        "QProgressBar {"
        "    border: 2px solid #ccc;"
        "    border-radius: 5px;"
        "    text-align: center;"
        "}"
        "QProgressBar::chunk {"
        "    background-color: #4CAF50;"
        "    border-radius: 3px;"
        "}"
    );

    layout->addWidget(m_analysisStatusLabel);
    layout->addWidget(m_analysisProgressBar);

    return m_analysisProgressGroup;
}

QGroupBox* MainWindow::createMemoryManagementGroup()
{
    m_memoryGroup = new QGroupBox("Memory Management");
    QVBoxLayout *layout = new QVBoxLayout(m_memoryGroup);

    // Memory button
    m_memoryStatsButton = new QPushButton("Show Statistics");
    connect(m_memoryStatsButton, &QPushButton::clicked, this, &MainWindow::showMemoryStats);

    // Memory usage
    m_memoryUsageLabel = new QLabel("Memory: 0%");
    m_memoryProgressBar = new QProgressBar;
    m_memoryProgressBar->setRange(0, 100);
    m_memoryProgressBar->setValue(0);

    layout->addWidget(m_memoryStatsButton);
    layout->addWidget(m_memoryUsageLabel);
    layout->addWidget(m_memoryProgressBar);

    return m_memoryGroup;
}

QGroupBox* MainWindow::createLogOutputGroup()
{
    m_logGroup = new QGroupBox("Log Output");
    QVBoxLayout *layout = new QVBoxLayout(m_logGroup);

    // Log text edit
    m_logTextEdit = new QTextEdit;
    m_logTextEdit->setReadOnly(true);
    m_logTextEdit->setFont(QFont("Monaco", 9));

    // Improve text brightness and readability with brighter colors
    m_logTextEdit->setStyleSheet(
        "QTextEdit {"
        "    background-color: #1e1e1e;"
        "    color: #f0f0f0;"
        "    border: 1px solid #555555;"
        "    selection-background-color: #404040;"
        "    font-size: 10pt;"
        "}"
    );

    // Clear button
    m_clearLogButton = new QPushButton("Clear Log");
    connect(m_clearLogButton, &QPushButton::clicked, m_logTextEdit, &QTextEdit::clear);

    QHBoxLayout *buttonLayout = new QHBoxLayout;
    buttonLayout->addStretch();
    buttonLayout->addWidget(m_clearLogButton);

    layout->addWidget(m_logTextEdit);
    layout->addLayout(buttonLayout);

    return m_logGroup;
}

void MainWindow::addLogMessage(const QString &message, bool isError)
{
    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
    QString formattedMessage = QString("[%1] %2").arg(timestamp, message);

    // Check if UI is initialized before accessing log text edit
    if (m_logTextEdit) {
        if (isError) {
            m_logTextEdit->setTextColor(QColor(255, 120, 120)); // Brighter red for errors
        } else {
            m_logTextEdit->setTextColor(QColor(240, 240, 240)); // Brighter white for normal text
        }

        m_logTextEdit->append(formattedMessage);

        // Auto-scroll to bottom
        QTextCursor cursor = m_logTextEdit->textCursor();
        cursor.movePosition(QTextCursor::End);
        m_logTextEdit->setTextCursor(cursor);
    } else {
        // If UI not ready, output to debug console
        qDebug() << "LOG:" << formattedMessage;
    }
}

void MainWindow::updateFileInfo()
{
    if (m_aafReader && m_aafReader->isLoaded()) {
        QVariantMap info = m_aafReader->fileInfo();
        QString text = QString("File: %1\nSize: %2\nTracks: %3\nRegions: %4")
                      .arg(info["fileName"].toString())
                      .arg(formatBytes(info["fileSize"].toLongLong()))
                      .arg(info["trackCount"].toInt())
                      .arg(info["regionCount"].toInt());
        m_fileInfoLabel->setText(text);

        // Update timecode display
        double duration = m_aafReader->duration();
        QString timecode = m_aafReader->formatTimecode(duration);
        QString frameRate = QString::number(m_aafReader->frameRate(), 'f', 2);
        m_timecodeLabel->setText(QString("Duration: %1 @ %2fps").arg(timecode, frameRate));

        m_closeFileButton->setEnabled(true);
    } else {
        m_fileInfoLabel->setText("No file loaded");
        m_timecodeLabel->setText("Duration: --:--:--.---");
        m_closeFileButton->setEnabled(false);
    }
}

void MainWindow::updateClassificationProgress(int progress, const QString &status)
{
    if (m_analysisStatusLabel) {
        m_analysisStatusLabel->setText(status);
    }

    if (m_analysisProgressBar) {
        if (progress >= 0 && progress <= 100) {
            m_analysisProgressBar->setVisible(true);
            m_analysisProgressBar->setValue(progress);
        } else {
            m_analysisProgressBar->setVisible(false);
        }
    }
}

void MainWindow::updateMemoryUsage()
{
    if (m_memoryManager) {
        QVariantMap stats = m_memoryManager->getMemoryStats();
        double usage = stats["usagePercentage"].toDouble();

        m_memoryUsageLabel->setText(QString("Memory: %1%").arg(usage, 0, 'f', 1));
        m_memoryProgressBar->setValue(int(usage));

        // Color code the progress bar
        if (usage > 90) {
            m_memoryProgressBar->setStyleSheet("QProgressBar::chunk { background-color: red; }");
        } else if (usage > 80) {
            m_memoryProgressBar->setStyleSheet("QProgressBar::chunk { background-color: orange; }");
        } else {
            m_memoryProgressBar->setStyleSheet("QProgressBar::chunk { background-color: green; }");
        }
    }
}

QString MainWindow::formatBytes(qint64 bytes)
{
    const qint64 KB = 1024;
    const qint64 MB = KB * 1024;
    const qint64 GB = MB * 1024;

    if (bytes >= GB) {
        return QString("%1 GB").arg(double(bytes) / GB, 0, 'f', 2);
    } else if (bytes >= MB) {
        return QString("%1 MB").arg(double(bytes) / MB, 0, 'f', 1);
    } else if (bytes >= KB) {
        return QString("%1 KB").arg(double(bytes) / KB, 0, 'f', 0);
    } else {
        return QString("%1 bytes").arg(bytes);
    }
}

QGroupBox* MainWindow::createProgressTrackingGroup()
{
    m_progressGroup = new QGroupBox("Analysis Progress");
    m_progressGroup->setVisible(false); // Hidden by default

    QHBoxLayout *layout = new QHBoxLayout(m_progressGroup);

    // Task and progress info
    QVBoxLayout *infoLayout = new QVBoxLayout;
    m_progressTaskLabel = new QLabel("Ready");
    m_progressTaskLabel->setStyleSheet("font-weight: bold;");

    m_mainProgressBar = new QProgressBar;
    m_mainProgressBar->setRange(0, 100);
    m_mainProgressBar->setValue(0);

    QHBoxLayout *timeLayout = new QHBoxLayout;
    m_progressTimeLabel = new QLabel("Elapsed: 00:00");
    m_progressETALabel = new QLabel("ETA: --:--");

    timeLayout->addWidget(m_progressTimeLabel);
    timeLayout->addStretch();
    timeLayout->addWidget(m_progressETALabel);

    infoLayout->addWidget(m_progressTaskLabel);
    infoLayout->addWidget(m_mainProgressBar);
    infoLayout->addLayout(timeLayout);

    // Control buttons
    QVBoxLayout *buttonLayout = new QVBoxLayout;
    m_pauseButton = new QPushButton("Pause");
    m_resumeButton = new QPushButton("Resume");
    m_stopButton = new QPushButton("Stop");

    m_pauseButton->setEnabled(false);
    m_resumeButton->setEnabled(false);
    m_stopButton->setEnabled(false);

    connect(m_pauseButton, &QPushButton::clicked, this, &MainWindow::pauseAnalysis);
    connect(m_resumeButton, &QPushButton::clicked, this, &MainWindow::resumeAnalysis);
    connect(m_stopButton, &QPushButton::clicked, this, &MainWindow::stopAnalysis);

    buttonLayout->addWidget(m_pauseButton);
    buttonLayout->addWidget(m_resumeButton);
    buttonLayout->addWidget(m_stopButton);

    layout->addLayout(infoLayout, 1);
    layout->addLayout(buttonLayout, 0);

    return m_progressGroup;
}

QWidget* MainWindow::createAnalysisSettingsTab()
{
    m_settingsTab = new QWidget;
    QVBoxLayout *mainLayout = new QVBoxLayout(m_settingsTab);

    // Quality presets group
    m_qualityGroup = new QGroupBox("Quality Presets");
    QVBoxLayout *qualityLayout = new QVBoxLayout(m_qualityGroup);

    m_qualityComboBox = new QComboBox;
    m_qualityComboBox->addItems({"Fast", "Balanced", "Accurate", "Maximum"});
    m_qualityComboBox->setCurrentText("Balanced");

    qualityLayout->addWidget(new QLabel("Analysis Quality:"));
    qualityLayout->addWidget(m_qualityComboBox);

    // Performance settings group
    m_performanceGroup = new QGroupBox("Performance Settings");
    QFormLayout *perfLayout = new QFormLayout(m_performanceGroup);

    m_chunkSkipSpinBox = new QSpinBox;
    m_chunkSkipSpinBox->setRange(1, 10);
    m_chunkSkipSpinBox->setValue(1);
    m_chunkSkipSpinBox->setSuffix(" (1=no skip)");

    m_maxConcurrentSpinBox = new QSpinBox;
    m_maxConcurrentSpinBox->setRange(1, 16);
    m_maxConcurrentSpinBox->setValue(2);

    m_fastModeCheckBox = new QCheckBox("Enable fast mode");

    perfLayout->addRow("Chunk Skip Factor:", m_chunkSkipSpinBox);
    perfLayout->addRow("Max Concurrent:", m_maxConcurrentSpinBox);
    perfLayout->addRow(m_fastModeCheckBox);

    // Classification settings group
    m_classificationGroup = new QGroupBox("Classification Settings");
    QFormLayout *classLayout = new QFormLayout(m_classificationGroup);

    m_confidenceSpinBox = new QDoubleSpinBox;
    m_confidenceSpinBox->setRange(0.0, 1.0);
    m_confidenceSpinBox->setSingleStep(0.1);
    m_confidenceSpinBox->setValue(0.6);
    m_confidenceSpinBox->setDecimals(1);

    m_speakerDiarizationCheckBox = new QCheckBox("Enable speaker diarization");
    m_speakerDiarizationCheckBox->setChecked(true);

    m_musicDetectionCheckBox = new QCheckBox("Enable music detection");
    m_musicDetectionCheckBox->setChecked(true);

    m_sfxDetectionCheckBox = new QCheckBox("Enable SFX detection");
    m_sfxDetectionCheckBox->setChecked(true);

    classLayout->addRow("Confidence Threshold:", m_confidenceSpinBox);
    classLayout->addRow(m_speakerDiarizationCheckBox);
    classLayout->addRow(m_musicDetectionCheckBox);
    classLayout->addRow(m_sfxDetectionCheckBox);

    // Time Range Selection group
    m_timeRangeGroup = new QGroupBox("Analysis Time Range");
    QVBoxLayout *timeRangeLayout = new QVBoxLayout(m_timeRangeGroup);

    m_analyzeFullAAFCheckBox = new QCheckBox("Analyze Full AAF File");
    m_analyzeFullAAFCheckBox->setChecked(true);
    timeRangeLayout->addWidget(m_analyzeFullAAFCheckBox);

    // Time range input controls
    QWidget *timeInputWidget = new QWidget;
    QFormLayout *timeInputLayout = new QFormLayout(timeInputWidget);

    m_startTimeEdit = new QLineEdit;
    m_startTimeEdit->setPlaceholderText(TimecodeUtils::getPlaceholderText(TimecodeUtils::Format::FRAMES));
    m_startTimeEdit->setEnabled(false);
    m_startTimeEdit->setToolTip("Start time in SMPTE format\nSupported formats: HH:MM:SS:FF or HH:MM:SS.mmm");

    m_endTimeEdit = new QLineEdit;
    m_endTimeEdit->setPlaceholderText(TimecodeUtils::getPlaceholderText(TimecodeUtils::Format::FRAMES));
    m_endTimeEdit->setEnabled(false);
    m_endTimeEdit->setToolTip("End time in SMPTE format\nSupported formats: HH:MM:SS:FF or HH:MM:SS.mmm");

    timeInputLayout->addRow("Start Time:", m_startTimeEdit);
    timeInputLayout->addRow("End Time:", m_endTimeEdit);

    timeRangeLayout->addWidget(timeInputWidget);

    // Time range buttons
    QHBoxLayout *timeButtonLayout = new QHBoxLayout;
    m_setFromTimelineButton = new QPushButton("Set from Timeline Selection");
    m_setFromTimelineButton->setEnabled(false);
    m_setFromTimelineButton->setToolTip("Get time range from timeline selection\n(Use Shift+drag on timeline ruler to select range)");
    m_clearTimeRangeButton = new QPushButton("Clear Range");
    m_clearTimeRangeButton->setEnabled(false);
    m_clearTimeRangeButton->setToolTip("Clear time range selection");

    timeButtonLayout->addWidget(m_setFromTimelineButton);
    timeButtonLayout->addWidget(m_clearTimeRangeButton);
    timeButtonLayout->addStretch();

    timeRangeLayout->addLayout(timeButtonLayout);

    // Connect time range signals
    connect(m_analyzeFullAAFCheckBox, &QCheckBox::toggled, this, &MainWindow::onTimeRangeChanged);
    connect(m_startTimeEdit, &QLineEdit::textChanged, this, &MainWindow::onTimeRangeChanged);
    connect(m_endTimeEdit, &QLineEdit::textChanged, this, &MainWindow::onTimeRangeChanged);
    connect(m_setFromTimelineButton, &QPushButton::clicked, this, &MainWindow::setTimeRangeFromTimeline);
    connect(m_clearTimeRangeButton, &QPushButton::clicked, this, &MainWindow::clearTimeRange);

    // Buttons
    QHBoxLayout *buttonLayout = new QHBoxLayout;
    m_resetSettingsButton = new QPushButton("Reset to Defaults");
    m_saveSettingsButton = new QPushButton("Apply Settings");

    connect(m_resetSettingsButton, &QPushButton::clicked, this, &MainWindow::resetAnalysisSettings);
    connect(m_saveSettingsButton, &QPushButton::clicked, this, &MainWindow::applyAnalysisSettings);

    buttonLayout->addWidget(m_resetSettingsButton);
    buttonLayout->addStretch();
    buttonLayout->addWidget(m_saveSettingsButton);

    // Add all groups to main layout
    mainLayout->addWidget(m_qualityGroup);
    mainLayout->addWidget(m_performanceGroup);
    mainLayout->addWidget(m_classificationGroup);
    mainLayout->addWidget(m_timeRangeGroup);
    mainLayout->addLayout(buttonLayout);
    mainLayout->addStretch();

    return m_settingsTab;
}

QWidget* MainWindow::createClassificationTab()
{
    qDebug() << "createClassificationTab: Starting";
    m_classificationTab = new QWidget;
    QVBoxLayout *mainLayout = new QVBoxLayout(m_classificationTab);

    qDebug() << "createClassificationTab: Creating classification controls";
    // Classification controls
    m_classificationControlGroup = new QGroupBox("Classification Control");
    QHBoxLayout *controlLayout = new QHBoxLayout(m_classificationControlGroup);

    qDebug() << "createClassificationTab: Creating buttons";
    m_startClassificationButton = new QPushButton("Start Classification");
    m_pauseClassificationButton = new QPushButton("Pause");
    m_stopClassificationButton = new QPushButton("Stop");
    m_analysisSettingsButton = new QPushButton("Analysis Settings");

    qDebug() << "createClassificationTab: Setting button states";
    m_pauseClassificationButton->setEnabled(false);
    m_stopClassificationButton->setEnabled(false);

    qDebug() << "createClassificationTab: Connecting button signals";
    connect(m_startClassificationButton, &QPushButton::clicked, this, &MainWindow::startClassification);
    connect(m_pauseClassificationButton, &QPushButton::clicked, this, &MainWindow::pauseAnalysis);
    connect(m_stopClassificationButton, &QPushButton::clicked, this, &MainWindow::stopAnalysis);
    connect(m_analysisSettingsButton, &QPushButton::clicked, this, &MainWindow::openAnalysisSettings);
    // Note: m_reviewClassificationButton connection moved to after button creation

    qDebug() << "createClassificationTab: Adding buttons to layout";
    controlLayout->addWidget(m_startClassificationButton);
    controlLayout->addWidget(m_pauseClassificationButton);
    controlLayout->addWidget(m_stopClassificationButton);
    controlLayout->addWidget(m_analysisSettingsButton);
    controlLayout->addStretch();

    qDebug() << "createClassificationTab: Creating status and progress";
    // Status and progress
    QVBoxLayout *statusLayout = new QVBoxLayout;
    m_classificationStatusLabel = new QLabel("Ready to classify regions");
    m_classificationProgressBar = new QProgressBar;
    m_classificationProgressBar->setVisible(false);

    statusLayout->addWidget(m_classificationStatusLabel);
    statusLayout->addWidget(m_classificationProgressBar);

    controlLayout->addLayout(statusLayout);

    qDebug() << "createClassificationTab: Creating results table";
    // Results table
    m_classificationResultsGroup = new QGroupBox("Classification Results");
    QVBoxLayout *resultsLayout = new QVBoxLayout(m_classificationResultsGroup);

    m_classificationTable = new QTableWidget;
    m_classificationTable->setColumnCount(9);
    QStringList headers = {"Region Name", "Track Name", "Content Type", "Confidence", "Speaker", "Start Time", "Duration", "Length", "Needs Review"};
    m_classificationTable->setHorizontalHeaderLabels(headers);
    m_classificationTable->horizontalHeader()->setStretchLastSection(true);
    m_classificationTable->setAlternatingRowColors(true);
    m_classificationTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_classificationTable->setSortingEnabled(true); // Enable column sorting

    // Connect classification table selection for bidirectional synchronization
    connect(m_classificationTable, &QTableWidget::itemSelectionChanged,
            this, &MainWindow::onClassificationTableSelectionChanged);

    // Improve table styling for better readability
    m_classificationTable->setStyleSheet(
        "QTableWidget {"
        "    background-color: #2b2b2b;"
        "    color: #ffffff;"
        "    gridline-color: #555555;"
        "    selection-background-color: #3d3d3d;"
        "}"
        "QTableWidget::item {"
        "    padding: 4px;"
        "    border-bottom: 1px solid #555555;"
        "}"
        "QHeaderView::section {"
        "    background-color: #404040;"
        "    color: #ffffff;"
        "    padding: 6px;"
        "    border: 1px solid #555555;"
        "    font-weight: bold;"
        "}"
    );

    qDebug() << "createClassificationTab: Creating result buttons";
    m_exportClassificationButton = new QPushButton("Export Results");
    m_exportClassificationButton->setEnabled(false);

    m_reviewClassificationButton = new QPushButton("Review & Correct");
    m_reviewClassificationButton->setEnabled(false);

    m_saveClassificationButton = new QPushButton("Save Data");
    m_saveClassificationButton->setEnabled(false);
    m_saveClassificationButton->setToolTip("Save classification data to JSON file");

    m_loadClassificationButton = new QPushButton("Load Data");
    m_loadClassificationButton->setToolTip("Load classification data from JSON file");

    qDebug() << "createClassificationTab: Connecting button signals";
    connect(m_reviewClassificationButton, &QPushButton::clicked, this, &MainWindow::openClassificationReview);
    connect(m_saveClassificationButton, &QPushButton::clicked, this, &MainWindow::saveClassificationData);
    connect(m_loadClassificationButton, &QPushButton::clicked, this, &MainWindow::loadClassificationData);

    QHBoxLayout *resultsButtonLayout = new QHBoxLayout;
    resultsButtonLayout->addWidget(m_reviewClassificationButton);
    resultsButtonLayout->addWidget(m_exportClassificationButton);
    resultsButtonLayout->addWidget(m_saveClassificationButton);
    resultsButtonLayout->addWidget(m_loadClassificationButton);
    resultsButtonLayout->addStretch();

    resultsLayout->addWidget(m_classificationTable);
    resultsLayout->addLayout(resultsButtonLayout);

    qDebug() << "createClassificationTab: Adding to main layout";
    // Add to main layout
    mainLayout->addWidget(m_classificationControlGroup);
    mainLayout->addWidget(m_classificationResultsGroup, 1);

    qDebug() << "createClassificationTab: Completed successfully";
    return m_classificationTab;
}

QWidget* MainWindow::createTrackOrganizationTab()
{
    m_organizationTab = new QWidget;
    QVBoxLayout *mainLayout = new QVBoxLayout(m_organizationTab);

    // Template selection
    m_templateGroup = new QGroupBox("Organization Template");
    QHBoxLayout *templateLayout = new QHBoxLayout(m_templateGroup);

    templateLayout->addWidget(new QLabel("Template:"));
    m_templateComboBox = new QComboBox;
    m_templateComboBox->addItems({"Standard", "Speaker-Based", "Content-Type", "Broadcast"});
    templateLayout->addWidget(m_templateComboBox);

    // Add preset management button
    m_managePresetsButton = new QPushButton("Manage Presets...");
    m_managePresetsButton->setToolTip("Create, edit, and manage organization presets");
    templateLayout->addWidget(m_managePresetsButton);

    m_preserveTimingCheckBox = new QCheckBox("Preserve original timing");
    m_preserveTimingCheckBox->setChecked(true);
    templateLayout->addWidget(m_preserveTimingCheckBox);

    templateLayout->addStretch();

    m_previewOrganizationButton = new QPushButton("Preview Organization");
    m_applyOrganizationButton = new QPushButton("Apply Organization");
    m_applyOrganizationButton->setEnabled(false);

    connect(m_previewOrganizationButton, &QPushButton::clicked, this, &MainWindow::previewTrackOrganization);
    connect(m_applyOrganizationButton, &QPushButton::clicked, this, &MainWindow::applyTrackOrganization);
    connect(m_managePresetsButton, &QPushButton::clicked, this, &MainWindow::openPresetManagement);

    templateLayout->addWidget(m_previewOrganizationButton);
    templateLayout->addWidget(m_applyOrganizationButton);

    // LLM Provider Selection
    m_llmProviderGroup = new QGroupBox("AI Assistant Provider");
    QHBoxLayout *llmLayout = new QHBoxLayout(m_llmProviderGroup);

    m_localLLMRadio = new QRadioButton("Local LLM (LM Studio)");
    m_onlineLLMRadio = new QRadioButton("Online LLM (OpenAI/Anthropic)");
    m_localLLMRadio->setChecked(m_llmSettingsData.useLocalLLM);
    m_onlineLLMRadio->setChecked(!m_llmSettingsData.useLocalLLM);

    m_llmSettingsButton = new QPushButton("LLM Settings");
    m_llmSettingsButton->setMaximumWidth(120);

    connect(m_localLLMRadio, &QRadioButton::toggled, this, &MainWindow::onLLMProviderChanged);
    connect(m_onlineLLMRadio, &QRadioButton::toggled, this, &MainWindow::onLLMProviderChanged);
    connect(m_llmSettingsButton, &QPushButton::clicked, this, &MainWindow::openLLMSettings);

    llmLayout->addWidget(m_localLLMRadio);
    llmLayout->addWidget(m_onlineLLMRadio);
    llmLayout->addStretch();
    llmLayout->addWidget(m_llmSettingsButton);

    // Results display with both table and timeline views
    m_organizationResultsGroup = new QGroupBox("Organization Preview");
    QVBoxLayout *resultsLayout = new QVBoxLayout(m_organizationResultsGroup);

    // Set proper layout constraints to prevent display issues
    resultsLayout->setContentsMargins(5, 5, 5, 5);
    resultsLayout->setSpacing(5);

    // Create tab widget for table and timeline views with proper size management
    m_organizationViewTabs = new QTabWidget;
    m_organizationViewTabs->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);

    // Table view tab
    QWidget *tableTab = new QWidget;
    QVBoxLayout *tableLayout = new QVBoxLayout(tableTab);

    m_organizationTable = new QTableWidget;
    m_organizationTable->setColumnCount(7);
    QStringList headers = {"Region Name", "Original Track", "Assigned Track", "Content Type", "Speaker", "Start Time", "Duration"};
    m_organizationTable->setHorizontalHeaderLabels(headers);
    m_organizationTable->horizontalHeader()->setStretchLastSection(true);
    m_organizationTable->setAlternatingRowColors(true);
    m_organizationTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_organizationTable->setSortingEnabled(true); // Enable Qt's built-in sorting (same as Classification tab)

    qDebug() << "Organization table created with built-in sorting enabled";

    // Connect organization table selection for bidirectional synchronization
    connect(m_organizationTable, &QTableWidget::itemSelectionChanged,
            this, &MainWindow::onOrganizationTableSelectionChanged);

    // Apply consistent styling with classification table
    m_organizationTable->setStyleSheet(
        "QTableWidget {"
        "    background-color: #2b2b2b;"
        "    color: #ffffff;"
        "    gridline-color: #555555;"
        "    selection-background-color: #3d3d3d;"
        "}"
        "QTableWidget::item {"
        "    padding: 4px;"
        "    border-bottom: 1px solid #555555;"
        "}"
        "QHeaderView::section {"
        "    background-color: #404040;"
        "    color: #ffffff;"
        "    padding: 6px;"
        "    border: 1px solid #555555;"
        "    font-weight: bold;"
        "}"
    );

    m_organizationStatsLabel = new QLabel("No organization preview available");
    m_organizationStatsLabel->setStyleSheet("QLabel { color: #666; font-style: italic; }");

    tableLayout->addWidget(m_organizationTable);
    tableLayout->addWidget(m_organizationStatsLabel);

    // Timeline view tab with proper layout management
    QWidget *timelineTab = new QWidget;
    QVBoxLayout *timelineLayout = new QVBoxLayout(timelineTab);
    timelineLayout->setContentsMargins(0, 0, 0, 0);
    timelineLayout->setSpacing(0);

    // Create organization timeline widget (similar to main timeline but shows organized tracks)
    m_organizationTimelineWidget = new TimelineWidget;
    m_organizationTimelineWidget->setAAFReader(m_aafReader);
    m_organizationTimelineWidget->setAudioFileManager(m_audioFileManager);
    m_organizationTimelineWidget->setAudioPlaybackManager(m_audioPlaybackManager);

    // Set proper size policy for timeline widget
    m_organizationTimelineWidget->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);

    // Connect organization timeline signals for bidirectional synchronization
    connect(m_organizationTimelineWidget, &TimelineWidget::regionSelected,
            this, &MainWindow::onOrganizationTimelineRegionSelected);
    connect(m_organizationTimelineWidget, &TimelineWidget::regionUpdated,
            this, &MainWindow::onOrganizationTimelineRegionUpdated);
    connect(m_organizationTimelineWidget, &TimelineWidget::timeRangeSelectionChanged,
            this, &MainWindow::onTimelineRangeSelected);

    timelineLayout->addWidget(m_organizationTimelineWidget);

    // Add tabs to tab widget
    m_organizationViewTabs->addTab(tableTab, "Table View");
    m_organizationViewTabs->addTab(timelineTab, "Timeline View");

    resultsLayout->addWidget(m_organizationViewTabs);

    // Add to main layout
    mainLayout->addWidget(m_templateGroup);
    mainLayout->addWidget(m_llmProviderGroup);
    mainLayout->addWidget(m_organizationResultsGroup, 1);

    return m_organizationTab;
}

QWidget* MainWindow::createExportTab()
{
    m_exportTab = new QWidget;
    QVBoxLayout *mainLayout = new QVBoxLayout(m_exportTab);

    // Export Format Selection
    m_exportFormatGroup = new QGroupBox("Export Format");
    QVBoxLayout *formatLayout = new QVBoxLayout(m_exportFormatGroup);

    m_aafExportRadio = new QRadioButton("AAF (Advanced Authoring Format)");
    m_omfExportRadio = new QRadioButton("OMF (Open Media Framework)");
    m_xmlExportRadio = new QRadioButton("XML (Final Cut Pro/Premiere)");
    m_csvExportRadio = new QRadioButton("CSV (Spreadsheet)");
    m_jsonExportRadio = new QRadioButton("JSON (Data Exchange)");
    m_aafExportRadio->setChecked(true); // Default to AAF

    connect(m_aafExportRadio, &QRadioButton::toggled, this, &MainWindow::onExportFormatChanged);
    connect(m_omfExportRadio, &QRadioButton::toggled, this, &MainWindow::onExportFormatChanged);
    connect(m_xmlExportRadio, &QRadioButton::toggled, this, &MainWindow::onExportFormatChanged);
    connect(m_csvExportRadio, &QRadioButton::toggled, this, &MainWindow::onExportFormatChanged);
    connect(m_jsonExportRadio, &QRadioButton::toggled, this, &MainWindow::onExportFormatChanged);

    formatLayout->addWidget(m_aafExportRadio);
    formatLayout->addWidget(m_omfExportRadio);
    formatLayout->addWidget(m_xmlExportRadio);
    formatLayout->addWidget(m_csvExportRadio);
    formatLayout->addWidget(m_jsonExportRadio);

    // Export Options
    m_exportOptionsGroup = new QGroupBox("Export Options");
    QVBoxLayout *optionsLayout = new QVBoxLayout(m_exportOptionsGroup);

    m_includeAudioCheckBox = new QCheckBox("Include Audio Files");
    m_includeMetadataCheckBox = new QCheckBox("Include Metadata");
    m_includeTimecodeCheckBox = new QCheckBox("Include Timecode Information");
    m_includeClassificationCheckBox = new QCheckBox("Include Classification Results");
    m_includeOrganizationCheckBox = new QCheckBox("Include Track Organization");

    // Set defaults
    m_includeAudioCheckBox->setChecked(true);
    m_includeMetadataCheckBox->setChecked(true);
    m_includeTimecodeCheckBox->setChecked(true);
    m_includeClassificationCheckBox->setChecked(true);
    m_includeOrganizationCheckBox->setChecked(true);

    optionsLayout->addWidget(m_includeAudioCheckBox);
    optionsLayout->addWidget(m_includeMetadataCheckBox);
    optionsLayout->addWidget(m_includeTimecodeCheckBox);
    optionsLayout->addWidget(m_includeClassificationCheckBox);
    optionsLayout->addWidget(m_includeOrganizationCheckBox);

    // Audio Quality Settings
    m_exportQualityGroup = new QGroupBox("Audio Quality");
    QFormLayout *qualityLayout = new QFormLayout(m_exportQualityGroup);

    m_audioQualityComboBox = new QComboBox;
    m_audioQualityComboBox->addItems({"High (Uncompressed)", "Medium (Lossless)", "Low (Compressed)"});

    m_sampleRateComboBox = new QComboBox;
    m_sampleRateComboBox->addItems({"48000 Hz", "44100 Hz", "96000 Hz", "192000 Hz"});

    m_bitDepthComboBox = new QComboBox;
    m_bitDepthComboBox->addItems({"24-bit", "16-bit", "32-bit float"});

    qualityLayout->addRow("Quality:", m_audioQualityComboBox);
    qualityLayout->addRow("Sample Rate:", m_sampleRateComboBox);
    qualityLayout->addRow("Bit Depth:", m_bitDepthComboBox);

    // Export Destination
    m_exportDestinationGroup = new QGroupBox("Export Destination");
    QVBoxLayout *destLayout = new QVBoxLayout(m_exportDestinationGroup);

    // Directory selection
    QHBoxLayout *pathLayout = new QHBoxLayout;
    m_exportPathLineEdit = new QLineEdit;
    m_exportPathLineEdit->setPlaceholderText("Select export directory...");
    m_browseExportPathButton = new QPushButton("Browse...");

    connect(m_browseExportPathButton, &QPushButton::clicked, this, &MainWindow::browseExportPath);

    pathLayout->addWidget(new QLabel("Directory:"));
    pathLayout->addWidget(m_exportPathLineEdit);
    pathLayout->addWidget(m_browseExportPathButton);

    destLayout->addLayout(pathLayout);

    // File name input
    QHBoxLayout *fileNameLayout = new QHBoxLayout;
    m_exportFileNameLineEdit = new QLineEdit;
    m_exportFileNameLineEdit->setPlaceholderText("Enter export file name...");
    m_exportFileNameLineEdit->setText("WAAFer_Export");

    connect(m_exportFileNameLineEdit, &QLineEdit::textChanged, this, &MainWindow::updateExportButtonState);

    fileNameLayout->addWidget(new QLabel("File Name:"));
    fileNameLayout->addWidget(m_exportFileNameLineEdit);

    destLayout->addLayout(fileNameLayout);

    m_startExportButton = new QPushButton("Start Export");
    m_startExportButton->setEnabled(false);
    connect(m_startExportButton, &QPushButton::clicked, this, &MainWindow::startExport);

    m_exportProgressBar = new QProgressBar;
    m_exportProgressBar->setVisible(false);

    m_exportStatusLabel = new QLabel("Ready to export");

    // Note: pathLayout and fileNameLayout already added above
    destLayout->addWidget(m_startExportButton);
    destLayout->addWidget(m_exportProgressBar);
    destLayout->addWidget(m_exportStatusLabel);

    // Export Time Range Selection
    m_exportTimeRangeGroup = new QGroupBox("Export Time Range");
    QVBoxLayout *exportTimeRangeLayout = new QVBoxLayout(m_exportTimeRangeGroup);

    m_exportFullAAFRadio = new QRadioButton("Export Full AAF File");
    m_exportFullAAFRadio->setChecked(true);
    exportTimeRangeLayout->addWidget(m_exportFullAAFRadio);

    m_exportAnalysisRangeRadio = new QRadioButton("Export Analysis Range");
    m_exportAnalysisRangeRadio->setToolTip("Use the time range selected in the Analysis tab");
    exportTimeRangeLayout->addWidget(m_exportAnalysisRangeRadio);

    m_exportCustomRangeRadio = new QRadioButton("Custom Export Range");
    exportTimeRangeLayout->addWidget(m_exportCustomRangeRadio);

    // Custom time range input
    QWidget *customTimeWidget = new QWidget;
    QFormLayout *customTimeLayout = new QFormLayout(customTimeWidget);

    m_exportStartTimeEdit = new QLineEdit;
    m_exportStartTimeEdit->setPlaceholderText(TimecodeUtils::getPlaceholderText(TimecodeUtils::Format::FRAMES));
    m_exportStartTimeEdit->setEnabled(false);

    m_exportEndTimeEdit = new QLineEdit;
    m_exportEndTimeEdit->setPlaceholderText(TimecodeUtils::getPlaceholderText(TimecodeUtils::Format::FRAMES));
    m_exportEndTimeEdit->setEnabled(false);

    customTimeLayout->addRow("Start Time:", m_exportStartTimeEdit);
    customTimeLayout->addRow("End Time:", m_exportEndTimeEdit);

    exportTimeRangeLayout->addWidget(customTimeWidget);

    // Connect export time range signals
    connect(m_exportFullAAFRadio, &QRadioButton::toggled, this, &MainWindow::onExportTimeRangeModeChanged);
    connect(m_exportAnalysisRangeRadio, &QRadioButton::toggled, this, &MainWindow::onExportTimeRangeModeChanged);
    connect(m_exportCustomRangeRadio, &QRadioButton::toggled, this, &MainWindow::onExportTimeRangeModeChanged);
    connect(m_exportStartTimeEdit, &QLineEdit::textChanged, this, &MainWindow::onExportTimeRangeModeChanged);
    connect(m_exportEndTimeEdit, &QLineEdit::textChanged, this, &MainWindow::onExportTimeRangeModeChanged);

    // Add to main layout
    mainLayout->addWidget(m_exportFormatGroup);
    mainLayout->addWidget(m_exportOptionsGroup);
    mainLayout->addWidget(m_exportQualityGroup);
    mainLayout->addWidget(m_exportTimeRangeGroup);
    mainLayout->addWidget(m_exportDestinationGroup);
    mainLayout->addStretch();

    return m_exportTab;
}

// Phase 3 slot implementations
void MainWindow::onProgressChanged()
{
    if (!m_progressTracker) return;

    double progress = m_progressTracker->progress();
    m_mainProgressBar->setValue(static_cast<int>(progress * 100));

    // Show/hide progress group based on activity
    bool isRunning = m_progressTracker->isRunning();
    m_progressGroup->setVisible(isRunning);

    // Update button states
    m_pauseButton->setEnabled(isRunning && !m_progressTracker->isPaused());
    m_resumeButton->setEnabled(isRunning && m_progressTracker->isPaused());
    m_stopButton->setEnabled(isRunning);
}

void MainWindow::onProgressTaskChanged()
{
    if (!m_progressTracker) return;

    m_progressTaskLabel->setText(m_progressTracker->currentTask());
}

void MainWindow::onProgressTimeChanged()
{
    if (!m_progressTracker) return;

    m_progressTimeLabel->setText(QString("Elapsed: %1").arg(m_progressTracker->elapsedTime()));
    m_progressETALabel->setText(QString("ETA: %1").arg(m_progressTracker->timeRemaining()));
}

void MainWindow::pauseAnalysis()
{
    if (m_progressTracker && m_progressTracker->isRunning()) {
        m_progressTracker->pauseProgress();
        addLogMessage("Analysis paused");
    }

    if (m_classificationEngine && m_classificationEngine->isClassifying()) {
        m_classificationEngine->pauseClassification();

        // Update button states for paused classification
        m_startClassificationButton->setText("Resume Classification");
        m_startClassificationButton->setEnabled(true);
        m_pauseClassificationButton->setEnabled(false);
        m_stopClassificationButton->setEnabled(true);
        m_classificationStatusLabel->setText("Classification paused - settings can be updated");
    }
}

void MainWindow::stopAnalysis()
{
    if (m_progressTracker && m_progressTracker->isRunning()) {
        m_progressTracker->stopProgress();
        addLogMessage("Analysis stopped");
    }

    if (m_classificationEngine && m_classificationEngine->isClassifying()) {
        m_classificationEngine->cancelClassification();

        // Reset button states after stopping
        m_startClassificationButton->setText("Start Classification");
        m_startClassificationButton->setEnabled(true);
        m_pauseClassificationButton->setEnabled(false);
        m_stopClassificationButton->setEnabled(false);
        m_classificationProgressBar->setVisible(false);
        m_classificationStatusLabel->setText("Classification stopped");
    }
}

void MainWindow::resumeAnalysis()
{
    if (m_progressTracker && m_progressTracker->isPaused()) {
        m_progressTracker->resumeProgress();
        addLogMessage("Analysis resumed");
    }

    if (m_classificationEngine) {
        m_classificationEngine->resumeClassification();

        // Update button states for resumed classification
        m_startClassificationButton->setText("Start Classification");
        m_startClassificationButton->setEnabled(false);
        m_pauseClassificationButton->setEnabled(true);
        m_stopClassificationButton->setEnabled(true);
        m_classificationProgressBar->setVisible(true);
        m_classificationStatusLabel->setText("Classification resumed...");
    }
}

void MainWindow::startClassification()
{
    qDebug() << "=== CLASSIFICATION TRIGGER DEBUG: startClassification called ===";

    qDebug() << "CLASSIFICATION TRIGGER DEBUG: Checking components availability";
    qDebug() << "CLASSIFICATION TRIGGER DEBUG: Classification engine available:" << (m_classificationEngine != nullptr);
    qDebug() << "CLASSIFICATION TRIGGER DEBUG: AAF reader available:" << (m_aafReader != nullptr);
    qDebug() << "CLASSIFICATION TRIGGER DEBUG: AAF reader loaded:" << (m_aafReader ? m_aafReader->isLoaded() : false);
    qDebug() << "CLASSIFICATION TRIGGER DEBUG: Audio file manager available:" << (m_audioFileManager != nullptr);

    if (!m_classificationEngine || !m_aafReader || !m_aafReader->isLoaded()) {
        qWarning() << "CLASSIFICATION TRIGGER DEBUG: Cannot start classification: No AAF file loaded";
        addLogMessage("Cannot start classification: No AAF file loaded", true);
        return;
    }

    if (!m_audioFileManager) {
        qWarning() << "CLASSIFICATION TRIGGER DEBUG: Cannot start classification: Audio file manager not available";
        addLogMessage("Cannot start classification: Audio file manager not available", true);
        return;
    }

    // Check if this is a resume operation
    bool isResume = (m_startClassificationButton->text() == "Resume Classification");
    qDebug() << "CLASSIFICATION TRIGGER DEBUG: Is resume operation:" << isResume;

    if (isResume) {
        qDebug() << "CLASSIFICATION TRIGGER DEBUG: Resuming analysis";
        // Apply any updated settings before resuming
        applyAnalysisSettings();
        resumeAnalysis();
        return;
    }

    // Get regions from AAF reader
    QVariantList allRegions = m_aafReader->getAllRegions();
    if (allRegions.isEmpty()) {
        addLogMessage("No regions found for classification", true);
        return;
    }

    // Filter regions based on time range selection
    QVariantList regions;
    if (m_timeRangeData.analyzeFullAAF) {
        regions = allRegions;
        addLogMessage(QString("Analyzing full AAF file with %1 regions").arg(allRegions.size()));
    } else if (m_timeRangeData.isValid()) {
        // Filter regions that overlap with the selected time range
        for (const QVariant &regionVariant : allRegions) {
            QVariantMap region = regionVariant.toMap();
            double regionStart = region.value("startTime").toDouble();
            double regionDuration = region.value("duration").toDouble();
            double regionEnd = regionStart + regionDuration;

            // Include regions that overlap with the selected time range
            if (regionEnd > m_timeRangeData.startTime && regionStart < m_timeRangeData.endTime) {
                regions.append(regionVariant);
            }
        }

        QString timeRangeStr = QString("%1 to %2")
            .arg(formatTimecode(m_timeRangeData.startTime))
            .arg(formatTimecode(m_timeRangeData.endTime));
        addLogMessage(QString("Analyzing time range %1: %2 of %3 regions selected")
                     .arg(timeRangeStr).arg(regions.size()).arg(allRegions.size()));
    } else {
        addLogMessage("Invalid time range specified", true);
        return;
    }

    if (regions.isEmpty()) {
        addLogMessage("No regions found in the specified time range", true);
        return;
    }

    // Debug: Log first few regions to verify data before classification
    addLogMessage(QString("Starting classification with %1 regions").arg(regions.size()));
    for (int i = 0; i < qMin(3, regions.size()); ++i) {
        QVariantMap region = regions[i].toMap();
        QString regionName = region.value("name").toString();
        QString trackName = region.value("track").toString();
        addLogMessage(QString("Classifying region: '%1' on track '%2'").arg(regionName).arg(trackName));
    }

    addLogMessage(QString("Starting classification of %1 regions").arg(regions.size()));
    addLogMessage("Resolving audio file paths...");

    // Resolve real audio file paths using AudioFileManager
    QVariantMap audioFilePaths;
    QString aafFilePath = m_aafReader->currentFile();
    int resolvedCount = 0;
    int mockCount = 0;
    int skippedCount = 0;

    for (const QVariant &regionVariant : regions) {
        QVariantMap region = regionVariant.toMap();
        QString regionId = region.value("id").toString();

        // Use AudioFileManager to resolve the audio file path
        QString audioPath = m_audioFileManager->getAudioFileForRegion(aafFilePath, region);

        if (!audioPath.isEmpty()) {
            audioFilePaths[regionId] = audioPath;

            if (audioPath.contains("mock_audio")) {
                mockCount++;
            } else {
                resolvedCount++;
            }
        } else {
            // Skip regions without audio files
            addLogMessage(QString("Warning: No audio file found for region %1").arg(regionId), true);
            skippedCount++;
        }
    }

    // If no real audio files found, allow classification to proceed with mock data fallback
    if (audioFilePaths.isEmpty()) {
        addLogMessage("⚠️ No real audio files found - classification will use mock data fallback", false);
        // Create empty audio file paths map - classification engine will handle mock data generation
        for (const auto& regionVariant : regions) {
            QVariantMap region = regionVariant.toMap();
            QString regionId = region.value("id").toString();
            audioFilePaths[regionId] = QString(); // Empty path triggers mock data fallback
        }
        mockCount = audioFilePaths.size();
    }

    addLogMessage(QString("Audio file resolution complete: %1 real files, %2 mock files, %3 skipped")
                 .arg(resolvedCount).arg(mockCount).arg(skippedCount));

    // Allocate memory for classification process
    if (m_memoryManager) {
        // Allocate memory chunks for classification workload
        int chunksToAllocate = qMin(audioFilePaths.size(), 10);
        for (int i = 0; i < chunksToAllocate; ++i) {
            QString chunkId = QString("classification_chunk_%1").arg(i);
            qint64 chunkSize = 50 * 1024 * 1024; // 50MB per chunk
            m_memoryManager->allocateChunk(chunkId, chunkSize);
        }
        addLogMessage(QString("Allocated memory for %1 classification chunks (%2 MB total)")
                     .arg(chunksToAllocate)
                     .arg(chunksToAllocate * 50));
    }

    // Start classification with resolved audio file paths
    qDebug() << "CLASSIFICATION TRIGGER DEBUG: About to call classifyRegions with" << regions.size() << "regions and" << audioFilePaths.size() << "audio files";
    qDebug() << "CLASSIFICATION TRIGGER DEBUG: Audio file paths keys:" << audioFilePaths.keys();

    m_classificationEngine->classifyRegions(regions, audioFilePaths);

    qDebug() << "CLASSIFICATION TRIGGER DEBUG: classifyRegions call completed, updating UI";

    // Update UI
    m_startClassificationButton->setEnabled(false);
    m_pauseClassificationButton->setEnabled(true);
    m_stopClassificationButton->setEnabled(true);
    m_classificationProgressBar->setVisible(true);
    m_classificationStatusLabel->setText(QString("Classifying %1 regions...").arg(audioFilePaths.size()));
    updateClassificationProgress(0, QString("Starting classification of %1 regions...").arg(audioFilePaths.size()));

    qDebug() << "CLASSIFICATION TRIGGER DEBUG: UI updated, classification should be running";
}

void MainWindow::onClassificationProgressChanged()
{
    if (m_classificationEngine) {
        double progress = m_classificationEngine->overallProgress();
        int progressPercent = static_cast<int>(progress * 100);

        QString message;
        if (progressPercent >= 100) {
            message = "Classification completed";
            updateClassificationProgress(-1, message);
        } else {
            message = QString("Classifying audio regions... %1%").arg(progressPercent);
            updateClassificationProgress(progressPercent, message);
        }
    }
}

void MainWindow::onClassificationCompleted(const QVariantList &results)
{
    addLogMessage(QString("Classification completed: %1 regions processed").arg(results.size()));

    // Update classification table
    m_classificationTable->setRowCount(results.size());

    for (int i = 0; i < results.size(); ++i) {
        QVariantMap result = results[i].toMap();
        QVariantMap metadata = result.value("metadata").toMap();

        // Region Name - improved extraction using same logic as timeline
        QString regionName = metadata.value("name").toString();
        QString audioFilePath = metadata.value("essenceFilePath", metadata.value("audioFilePath")).toString();

        // If region name is generic or empty, try to extract from audio file path
        if (regionName.isEmpty() || regionName == "Clip" || regionName.startsWith("region_")) {
            if (!audioFilePath.isEmpty()) {
                QFileInfo fileInfo(audioFilePath);
                regionName = fileInfo.baseName(); // Use filename without extension
            }
        }

        // Fallback to regionId if still no good name
        if (regionName.isEmpty()) {
            regionName = result.value("regionId", QString("Region_%1").arg(i + 1)).toString();
        }

        // Add mock data indicator for region names
        QTableWidgetItem *regionItem = new QTableWidgetItem(regionName);
        if (regionName.contains("❌") || regionName.contains("MOCK") || regionName.contains("FAKE")) {
            regionItem->setForeground(QColor(255, 150, 150)); // Light red for mock data
            regionItem->setToolTip("Mock data - no real AAF region name available");
        }
        m_classificationTable->setItem(i, 0, regionItem);

        // Track Name - use correct field names from LibAAF
        QString trackName = metadata.value("trackName", metadata.value("track")).toString();
        if (trackName.isEmpty()) {
            trackName = result.value("trackName", result.value("track", QString("Track_%1").arg((i % 8) + 1))).toString();
        }

        // Add mock data indicator for track names
        QTableWidgetItem *trackItem = new QTableWidgetItem(trackName);
        if (trackName.contains("❌") || trackName.contains("MOCK") || trackName.contains("FAKE")) {
            trackItem->setForeground(QColor(255, 150, 150)); // Light red for mock data
            trackItem->setToolTip("Mock data - no real AAF track name available");
        }
        m_classificationTable->setItem(i, 1, trackItem);

        // Content Type with mock data indicator
        QString contentType = result.value("contentType").toString();
        QTableWidgetItem *contentItem = new QTableWidgetItem(contentType);
        if (contentType.contains("❌") || contentType.contains("MOCK") || contentType.contains("Unknown")) {
            contentItem->setForeground(QColor(255, 150, 150)); // Light red for mock data
            contentItem->setToolTip("Mock classification result");
        }
        m_classificationTable->setItem(i, 2, contentItem);

        // Confidence (as percentage) with mock data indicator
        double confidence = result.value("confidence").toDouble();
        QString confidenceStr = QString::number(confidence * 100, 'f', 1) + "%";
        QTableWidgetItem *confidenceItem = new QTableWidgetItem(confidenceStr);
        if (confidence == 0.0 || contentType.contains("❌") || contentType.contains("MOCK")) {
            confidenceItem->setForeground(QColor(255, 150, 150)); // Light red for mock data
            confidenceItem->setToolTip("Mock confidence value");
        } else if (confidence < 0.5) {
            confidenceItem->setForeground(QColor(255, 200, 100)); // Yellow for low confidence
        } else if (confidence > 0.8) {
            confidenceItem->setForeground(QColor(100, 255, 100)); // Green for high confidence
        }
        m_classificationTable->setItem(i, 3, confidenceItem);

        // Speaker ID with enhanced display
        QString speakerId = result.value("speakerId").toString();
        QTableWidgetItem *speakerItem = new QTableWidgetItem(speakerId);

        // Color-code speakers for better visual distinction
        if (!speakerId.isEmpty() && !speakerId.contains("❌") && !speakerId.contains("MOCK")) {
            // Generate consistent colors for each speaker
            uint speakerHash = qHash(speakerId);
            QColor speakerColor = QColor::fromHsv((speakerHash % 360), 100, 200);
            speakerItem->setBackground(speakerColor);
            speakerItem->setForeground(QColor(0, 0, 0)); // Black text for readability
        } else if (speakerId.contains("❌") || speakerId.contains("MOCK") || speakerId.contains("FAKE")) {
            speakerItem->setForeground(QColor(255, 150, 150)); // Light red for mock data
            speakerItem->setToolTip("Mock speaker data");
        }

        m_classificationTable->setItem(i, 4, speakerItem);

        // Start Time (formatted as HH:MM:SS.mmm) - use LibAAF field names
        double startTime = metadata.value("position", metadata.value("startTime", result.value("startTime", 0.0))).toDouble();
        QString timeStr = formatTimecode(startTime);
        m_classificationTable->setItem(i, 5, new QTableWidgetItem(timeStr));

        // Duration (formatted as seconds) - use LibAAF field names
        double duration = metadata.value("length", metadata.value("duration", result.value("duration", 0.0))).toDouble();
        QString durationStr = QString::number(duration, 'f', 2) + "s";
        m_classificationTable->setItem(i, 6, new QTableWidgetItem(durationStr));

        // Length (formatted as HH:MM:SS.mmm)
        QString lengthStr = formatTimecode(duration);
        m_classificationTable->setItem(i, 7, new QTableWidgetItem(lengthStr));

        // Needs Review
        bool needsReview = result.value("needsReview").toBool();
        QTableWidgetItem *reviewItem = new QTableWidgetItem(needsReview ? "Yes" : "No");
        if (needsReview) {
            reviewItem->setBackground(QColor(255, 200, 200));
        } else {
            reviewItem->setBackground(QColor(200, 255, 200));
        }
        m_classificationTable->setItem(i, 8, reviewItem);
    }

    // Update timeline with classification results
    if (m_timelineWidget) {
        for (const QVariant &resultVariant : results) {
            QVariantMap result = resultVariant.toMap();
            QString regionId = result.value("regionId").toString();

            QVariantMap classification;
            classification["contentType"] = result.value("contentType");
            classification["speakerId"] = result.value("speakerId");
            classification["confidence"] = result.value("confidence");

            m_timelineWidget->updateRegionClassification(regionId, classification);
        }
        addLogMessage("Timeline updated with classification results");
    }

    // Display speaker statistics
    if (m_classificationEngine) {
        QVariantMap speakerStats = m_classificationEngine->getSpeakerStatistics();
        int totalSpeakers = speakerStats.value("totalSpeakers", 0).toInt();
        double totalSpeechTime = speakerStats.value("totalSpeechTime", 0.0).toDouble();

        if (totalSpeakers > 0) {
            QString speakerSummary = QString("Detected %1 speaker(s) with %.1f seconds of total speech")
                                   .arg(totalSpeakers)
                                   .arg(totalSpeechTime);
            addLogMessage(speakerSummary);

            // Log individual speaker details
            QVariantList speakers = speakerStats.value("speakers").toList();
            for (const QVariant &speakerVar : speakers) {
                QVariantMap speaker = speakerVar.toMap();
                QString speakerId = speaker.value("speakerId").toString();
                int regionCount = speaker.value("regionCount").toInt();
                double duration = speaker.value("totalDuration").toDouble();
                double confidence = speaker.value("averageConfidence").toDouble();

                QString speakerDetail = QString("  %1: %2 regions, %.1fs duration, %.1f%% avg confidence")
                                      .arg(speakerId)
                                      .arg(regionCount)
                                      .arg(duration)
                                      .arg(confidence * 100);
                addLogMessage(speakerDetail);
            }
        } else {
            addLogMessage("No speakers detected in speech regions");
        }

        // Update track organizer with speaker database
        if (m_trackOrganizer) {
            m_trackOrganizer->setSpeakerDatabase(speakerStats);
            m_trackOrganizer->setPerformanceOptimizations(true);
            addLogMessage("Track organizer updated with speaker database");
        }
    }

    // Clean up memory allocation for classification
    if (m_memoryManager) {
        // Deallocate classification chunks
        for (int i = 0; i < 10; ++i) {
            QString chunkId = QString("classification_chunk_%1").arg(i);
            if (m_memoryManager->isChunkLoaded(chunkId)) {
                m_memoryManager->deallocateChunk(chunkId);
            }
        }
        addLogMessage("Released classification memory chunks");
    }

    // Update UI
    m_startClassificationButton->setEnabled(true);
    m_pauseClassificationButton->setEnabled(false);
    m_stopClassificationButton->setEnabled(false);
    m_classificationProgressBar->setVisible(false);
    m_classificationStatusLabel->setText(QString("Classification completed: %1 regions").arg(results.size()));
    m_exportClassificationButton->setEnabled(true);
    m_reviewClassificationButton->setEnabled(true);
    m_saveClassificationButton->setEnabled(true);
}

void MainWindow::onClassificationError(const QString &regionId, const QString &error)
{
    addLogMessage(QString("Classification error for region %1: %2").arg(regionId, error), true);
}

void MainWindow::applyAnalysisSettings()
{
    if (!m_analysisSettings) return;

    // Apply quality preset from settings data
    if (m_settingsData.quality == "Fast") {
        m_analysisSettings->applyQualityPreset(AnalysisSettings::Fast);
    } else if (m_settingsData.quality == "Balanced") {
        m_analysisSettings->applyQualityPreset(AnalysisSettings::Balanced);
    } else if (m_settingsData.quality == "Accurate") {
        m_analysisSettings->applyQualityPreset(AnalysisSettings::Accurate);
    } else if (m_settingsData.quality == "Maximum") {
        m_analysisSettings->applyQualityPreset(AnalysisSettings::Maximum);
    }

    // Apply individual settings from settings data
    m_analysisSettings->setChunkSkipFactor(m_settingsData.chunkSkipFactor);
    m_analysisSettings->setMaxConcurrentAnalysis(m_settingsData.maxConcurrent);
    m_analysisSettings->setEnableFastMode(m_settingsData.fastMode);
    m_analysisSettings->setConfidenceThreshold(m_settingsData.confidenceThreshold);
    m_analysisSettings->setEnableSpeakerDiarization(m_settingsData.speakerDiarization);
    m_analysisSettings->setEnableMusicDetection(m_settingsData.musicDetection);
    m_analysisSettings->setEnableSFXDetection(m_settingsData.sfxDetection);

    addLogMessage("Analysis settings applied");
}

void MainWindow::resetAnalysisSettings()
{
    if (!m_analysisSettings) return;

    m_analysisSettings->resetToDefaults();

    // Reset settings data to defaults
    m_settingsData.quality = "Balanced";
    m_settingsData.chunkSkipFactor = 1;
    m_settingsData.maxConcurrent = 2;
    m_settingsData.fastMode = false;
    m_settingsData.confidenceThreshold = 0.6;
    m_settingsData.speakerDiarization = true;
    m_settingsData.musicDetection = true;
    m_settingsData.sfxDetection = true;
    m_speakerDiarizationCheckBox->setChecked(true);
    m_musicDetectionCheckBox->setChecked(true);
    m_sfxDetectionCheckBox->setChecked(true);

    // Reset time range to defaults
    m_timeRangeData.analyzeFullAAF = true;
    m_timeRangeData.startTime = 0.0;
    m_timeRangeData.endTime = 0.0;
    m_analyzeFullAAFCheckBox->setChecked(true);
    m_startTimeEdit->clear();
    m_endTimeEdit->clear();
    onTimeRangeChanged();

    addLogMessage("Analysis settings reset to defaults");
}

void MainWindow::previewTrackOrganization()
{
    qDebug() << "=== PREVIEW ORGANIZATION BUTTON CLICKED ===";
    addLogMessage("Preview Organization button clicked");

    if (!m_trackOrganizer || !m_aafReader || !m_aafReader->isLoaded()) {
        qDebug() << "PREVIEW ERROR: Missing components - trackOrganizer:" << (m_trackOrganizer != nullptr)
                 << "aafReader:" << (m_aafReader != nullptr)
                 << "loaded:" << (m_aafReader ? m_aafReader->isLoaded() : false);
        addLogMessage("Cannot preview organization: No AAF file loaded", true);
        return;
    }

    qDebug() << "PREVIEW: All components available, proceeding with organization";

    // Clear organization cache to force fresh organization
    m_trackOrganizer->setPerformanceOptimizations(false);
    m_trackOrganizer->setPerformanceOptimizations(true);

    // Get regions - prefer classified regions from classification table
    QVariantList regions;
    qDebug() << "PREVIEW: Checking classification table - row count:" << m_classificationTable->rowCount();

    if (m_classificationTable->rowCount() > 0) {
        qDebug() << "PREVIEW: Using real classification results";
        // Use classified regions from classification table
        for (int i = 0; i < m_classificationTable->rowCount(); ++i) {
            QVariantMap region;

            // Use the actual region ID from the table's UserRole data
            QTableWidgetItem *nameItem = m_classificationTable->item(i, 0);
            if (nameItem && !nameItem->data(Qt::UserRole).toString().isEmpty()) {
                region["id"] = nameItem->data(Qt::UserRole).toString();
            } else {
                region["id"] = QString("region_%1").arg(i);
            }

            region["name"] = m_classificationTable->item(i, 0)->text();
            region["track"] = m_classificationTable->item(i, 1)->text();
            region["contentType"] = m_classificationTable->item(i, 2)->text();
            region["speakerId"] = m_classificationTable->item(i, 4)->text();

            // Parse timecode from table
            QString timeStr = m_classificationTable->item(i, 5)->text();
            QStringList timeParts = timeStr.split(':');
            if (timeParts.size() >= 3) {
                double hours = timeParts[0].toDouble();
                double minutes = timeParts[1].toDouble();
                QStringList secParts = timeParts[2].split('.');
                double seconds = secParts[0].toDouble();
                double milliseconds = secParts.size() > 1 ? secParts[1].toDouble() / 1000.0 : 0.0;
                region["startTime"] = hours * 3600 + minutes * 60 + seconds + milliseconds;
            }

            // Parse duration
            QString durationStr = m_classificationTable->item(i, 6)->text().remove('s');
            region["duration"] = durationStr.toDouble();

            // Parse confidence
            QString confidenceStr = m_classificationTable->item(i, 3)->text().remove('%');
            region["confidence"] = confidenceStr.toDouble() / 100.0;

            regions.append(region);
        }
        qDebug() << "PREVIEW: Collected" << regions.size() << "classified regions";
        addLogMessage(QString("Using %1 classified regions for organization preview").arg(regions.size()));
    } else {
        addLogMessage("No classification results available - please run classification first", true);
        return;
    }

    // Validate regions have required fields
    for (int i = 0; i < regions.size(); ++i) {
        QVariantMap region = regions[i].toMap();
        if (!region.contains("contentType") || region.value("contentType").toString().isEmpty()) {
            qWarning() << "Region" << i << "missing contentType, setting to Unknown";
            region["contentType"] = "Unknown";
            regions[i] = region;
        }
        if (!region.contains("startTime")) {
            qWarning() << "Region" << i << "missing startTime, setting to 0";
            region["startTime"] = 0.0;
            regions[i] = region;
        }
        if (!region.contains("duration")) {
            qWarning() << "Region" << i << "missing duration, setting to 1.0";
            region["duration"] = 1.0;
            regions[i] = region;
        }
    }

    QString templateName = m_templateComboBox->currentText();
    qDebug() << "PREVIEW: Selected template:" << templateName;
    qDebug() << "PREVIEW: Preserve timing:" << m_preserveTimingCheckBox->isChecked();

    m_trackOrganizer->setCurrentTemplate(templateName);
    m_trackOrganizer->setPreserveOriginalTiming(m_preserveTimingCheckBox->isChecked());

    qDebug() << "PREVIEW: Calling optimizeOrganization with" << regions.size() << "regions";

    // Debug: Log sample input regions
    qDebug() << "PREVIEW DEBUG: Sample input regions:";
    int sampleCount = qMin(3, regions.size());
    for (int i = 0; i < sampleCount; ++i) {
        QVariantMap region = regions[i].toMap();
        qDebug() << "  Input Region" << i << ":" << region.value("name").toString()
                 << "contentType:" << region.value("contentType").toString()
                 << "speakerId:" << region.value("speakerId").toString()
                 << "track:" << region.value("track").toString();
    }

    // Use optimized organization for better performance
    QVariantList assignments = m_trackOrganizer->optimizeOrganization(regions);
    qDebug() << "PREVIEW: Organization returned" << assignments.size() << "assignments";

    if (assignments.isEmpty()) {
        addLogMessage("Track organization failed - no assignments generated", true);
        qDebug() << "PREVIEW ERROR: No assignments returned from organizer";

        // Debug the template and regions
        qDebug() << "Template name:" << templateName;
        qDebug() << "Available templates:" << m_trackOrganizer->availableTemplates();
        qDebug() << "Input regions count:" << regions.size();
        if (!regions.isEmpty()) {
            QVariantMap firstRegion = regions.first().toMap();
            qDebug() << "First region keys:" << firstRegion.keys();
            qDebug() << "First region contentType:" << firstRegion.value("contentType");
        }
        return;
    }

    // Debug: Log ALL assignments to understand what's being generated
    qDebug() << "PREVIEW DEBUG: ALL assignments:";
    for (int i = 0; i < assignments.size(); ++i) {
        QVariantMap assignment = assignments[i].toMap();
        qDebug() << "  Assignment" << i << ":"
                 << "regionName:" << assignment.value("regionName").toString()
                 << "assignedTrack:" << assignment.value("assignedTrack").toString()
                 << "contentType:" << assignment.value("contentType").toString()
                 << "speakerId:" << assignment.value("speakerId").toString()
                 << "originalTrack:" << assignment.value("originalTrack").toString();
    }

    // Get detailed analytics
    QVariantMap analytics = m_trackOrganizer->getDetailedAnalytics(assignments);

    // Log analytics information
    if (analytics.contains("speakerAnalytics")) {
        QVariantMap speakerAnalytics = analytics.value("speakerAnalytics").toMap();
        int uniqueSpeakers = speakerAnalytics.value("uniqueSpeakers", 0).toInt();
        addLogMessage(QString("Organization analytics: %1 unique speakers detected").arg(uniqueSpeakers));
    }

    if (analytics.contains("trackEfficiency")) {
        QVariantMap efficiency = analytics.value("trackEfficiency").toMap();
        double efficiencyScore = efficiency.value("efficiencyScore", 0.0).toDouble();
        int totalTracks = efficiency.value("totalTracks", 0).toInt();
        addLogMessage(QString("Track efficiency: %1% with %2 tracks").arg(efficiencyScore, 0, 'f', 1).arg(totalTracks));
    }

    // Count unique tracks created
    QSet<QString> uniqueTracks;
    for (const QVariant &assignmentVariant : assignments) {
        QVariantMap assignment = assignmentVariant.toMap();
        QString trackName = assignment.value("assignedTrack").toString();
        if (!trackName.isEmpty()) {
            uniqueTracks.insert(trackName);
        }
    }
    addLogMessage(QString("Organization created %1 unique tracks from %2 regions").arg(uniqueTracks.size()).arg(assignments.size()));

    // Update organization table
    qDebug() << "PREVIEW: Updating organization table with" << assignments.size() << "assignments";
    m_organizationTable->setRowCount(assignments.size());

    for (int i = 0; i < assignments.size(); ++i) {
        QVariantMap assignment = assignments[i].toMap();

        qDebug() << "PREVIEW: Processing assignment" << i << ":" << assignment;

        // Use region name instead of ID for consistency with Classification tab
        QString regionName = assignment.value("regionName", assignment.value("regionId")).toString();
        QString originalTrack = assignment.value("originalTrack").toString();
        QString assignedTrack = assignment.value("assignedTrack").toString();
        QString contentType = assignment.value("contentType").toString();
        QString speakerId = assignment.value("speakerId").toString();

        qDebug() << "PREVIEW: Row" << i << "- Region:" << regionName
                 << "Original:" << originalTrack << "Assigned:" << assignedTrack
                 << "Content:" << contentType << "Speaker:" << speakerId;

        QTableWidgetItem *regionItem = new QTableWidgetItem(regionName);
        regionItem->setForeground(QColor(255, 255, 255)); // Consistent white text
        m_organizationTable->setItem(i, 0, regionItem);

        // Original Track
        QTableWidgetItem *originalTrackItem = new QTableWidgetItem(originalTrack);
        originalTrackItem->setForeground(QColor(255, 255, 255));
        m_organizationTable->setItem(i, 1, originalTrackItem);

        // Assigned Track
        QTableWidgetItem *assignedTrackItem = new QTableWidgetItem(assignedTrack);
        assignedTrackItem->setForeground(QColor(255, 255, 255));
        m_organizationTable->setItem(i, 2, assignedTrackItem);

        // Content Type
        QTableWidgetItem *contentTypeItem = new QTableWidgetItem(contentType);
        contentTypeItem->setForeground(QColor(255, 255, 255));
        m_organizationTable->setItem(i, 3, contentTypeItem);

        // Speaker ID
        QTableWidgetItem *speakerItem = new QTableWidgetItem(speakerId);
        speakerItem->setForeground(QColor(255, 255, 255));
        m_organizationTable->setItem(i, 4, speakerItem);

        // Start Time - display as professional timecode but store numerical data for sorting
        double startTime = assignment.value("startTime").toDouble();
        QString startTimeStr = TimecodeUtils::formatTimecode(startTime,
                                                           TimecodeUtils::Format::FRAMES,
                                                           TimecodeUtils::FrameRate::FPS_25);
        QTableWidgetItem *startTimeItem = new QTableWidgetItem(startTimeStr);
        startTimeItem->setData(Qt::ItemDataRole::UserRole, startTime); // Store numerical value for Qt sorting
        startTimeItem->setForeground(QColor(255, 255, 255));
        m_organizationTable->setItem(i, 5, startTimeItem);

        // Duration - display as professional timecode but store numerical data for sorting
        double duration = assignment.value("duration").toDouble();
        QString durationStr = TimecodeUtils::formatTimecode(duration,
                                                          TimecodeUtils::Format::FRAMES,
                                                          TimecodeUtils::FrameRate::FPS_25);
        QTableWidgetItem *durationItem = new QTableWidgetItem(durationStr);
        durationItem->setData(Qt::ItemDataRole::UserRole, duration); // Store numerical value for Qt sorting
        durationItem->setForeground(QColor(255, 255, 255));
        m_organizationTable->setItem(i, 6, durationItem);
    }

    m_organizationStatsLabel->setText(QString("Preview: %1 regions organized using %2 template")
                                     .arg(assignments.size()).arg(templateName));
    m_applyOrganizationButton->setEnabled(true);

    // Update organization timeline view with organized tracks
    updateOrganizationTimeline(assignments);

    addLogMessage(QString("Organization preview generated: %1 regions").arg(assignments.size()));
}

void MainWindow::updateOrganizationTimeline(const QVariantList &assignments)
{
    qDebug() << "=== UPDATE ORGANIZATION TIMELINE CALLED ===";
    qDebug() << "Assignments count:" << assignments.size();

    if (!m_organizationTimelineWidget) {
        qWarning() << "Organization timeline widget not available";
        return;
    }

    // Convert assignments to timeline format
    QVariantList organizedRegions;
    QVariantList organizedTracks;
    QMap<QString, QVariantList> trackRegions; // Group regions by assigned track

    // Group regions by their assigned tracks
    for (const auto &assignmentVar : assignments) {
        QVariantMap assignment = assignmentVar.toMap();
        QString assignedTrack = assignment.value("assignedTrack").toString();

        // Create region data for timeline
        QVariantMap region;
        region["id"] = assignment.value("regionId", assignment.value("regionName")).toString();
        region["name"] = assignment.value("regionName").toString();
        region["trackName"] = assignedTrack;
        region["position"] = assignment.value("startTime").toDouble();
        region["length"] = assignment.value("duration").toDouble();
        region["contentType"] = assignment.value("contentType").toString();
        region["speakerId"] = assignment.value("speakerId").toString();

        trackRegions[assignedTrack].append(region);
        organizedRegions.append(region);
    }

    // Create track data
    for (auto it = trackRegions.begin(); it != trackRegions.end(); ++it) {
        QVariantMap track;
        track["name"] = it.key();
        track["originalName"] = it.key(); // Set originalName for proper track-region matching
        track["type"] = "Organized";
        track["visible"] = true;
        track["muted"] = false;
        track["solo"] = false;
        track["selected"] = true;
        organizedTracks.append(track);
    }

    // Load organized data into timeline
    qDebug() << "ORGANIZATION DEBUG: About to call loadTimelineData with:"
             << organizedRegions.size() << "regions and" << organizedTracks.size() << "tracks";

    if (organizedTracks.isEmpty()) {
        qWarning() << "ORGANIZATION ERROR: No tracks created from assignments!";
        addLogMessage("Organization failed: No tracks were created", true);
        return;
    }

    if (organizedRegions.isEmpty()) {
        qWarning() << "ORGANIZATION ERROR: No regions created from assignments!";
        addLogMessage("Organization failed: No regions were created", true);
        return;
    }

    m_organizationTimelineWidget->loadTimelineData(organizedRegions, organizedTracks);

    // Force refresh the timeline widget
    m_organizationTimelineWidget->refreshTimeline();

    qDebug() << "Organization timeline updated with" << organizedRegions.size()
             << "regions across" << organizedTracks.size() << "organized tracks";

    // Debug: Log track names and region assignments for verification
    qDebug() << "ORGANIZATION DEBUG: Track names created:";
    for (const auto &trackVar : organizedTracks) {
        QVariantMap track = trackVar.toMap();
        qDebug() << "  Track:" << track.value("name").toString()
                 << "originalName:" << track.value("originalName").toString();
    }

    qDebug() << "ORGANIZATION DEBUG: Sample region assignments:";
    int sampleCount = qMin(5, organizedRegions.size());
    for (int i = 0; i < sampleCount; ++i) {
        QVariantMap region = organizedRegions[i].toMap();
        qDebug() << "  Region:" << region.value("name").toString()
                 << "trackName:" << region.value("trackName").toString()
                 << "contentType:" << region.value("contentType").toString();
    }
}

void MainWindow::applyTrackOrganization()
{
    if (!m_trackOrganizer || !m_aafReader || !m_aafReader->isLoaded()) {
        addLogMessage("Cannot apply organization: No AAF file loaded", true);
        return;
    }

    // Get current organization preview
    QVariantList assignments;
    for (int i = 0; i < m_organizationTable->rowCount(); ++i) {
        QVariantMap assignment;
        assignment["regionName"] = m_organizationTable->item(i, 0)->text();
        assignment["regionId"] = m_organizationTable->item(i, 0)->text(); // Keep for backward compatibility
        assignment["originalTrack"] = m_organizationTable->item(i, 1)->text();
        assignment["assignedTrack"] = m_organizationTable->item(i, 2)->text();
        assignment["contentType"] = m_organizationTable->item(i, 3)->text();
        assignment["speakerId"] = m_organizationTable->item(i, 4)->text();
        assignment["startTime"] = m_organizationTable->item(i, 5)->text().toDouble();
        assignment["duration"] = m_organizationTable->item(i, 6)->text().toDouble();
        assignments.append(assignment);
    }

    if (assignments.isEmpty()) {
        addLogMessage("No organization assignments to apply", true);
        return;
    }

    QString templateName = m_templateComboBox->currentText();
    bool preserveTiming = m_preserveTimingCheckBox->isChecked();

    addLogMessage(QString("Applying track organization: %1 assignments using %2 template")
                 .arg(assignments.size()).arg(templateName));

    // Apply the organization (this would modify the AAF file in a real implementation)
    // For now, we'll use organizeRegions to simulate the application
    QVariantList regions;
    for (const QVariant &assignment : assignments) {
        QVariantMap assignmentMap = assignment.toMap();
        QVariantMap region;
        region["id"] = assignmentMap["regionId"];
        region["contentType"] = assignmentMap["contentType"];
        region["speakerId"] = assignmentMap["speakerId"];
        region["startTime"] = assignmentMap["startTime"];
        region["duration"] = assignmentMap["duration"];
        regions.append(region);
    }

    QVariantList originalTracks; // Empty for now
    QVariantList finalAssignments = m_trackOrganizer->optimizeOrganization(regions);

    // Get final analytics
    QVariantMap finalAnalytics = m_trackOrganizer->getDetailedAnalytics(finalAssignments);

    // Log final organization results
    int totalTracks = finalAnalytics.value("totalTracks", 0).toInt();
    int conflictCount = finalAnalytics.value("conflictCount", 0).toInt();
    double efficiencyScore = finalAnalytics.value("trackEfficiency").toMap().value("efficiencyScore", 0.0).toDouble();

    addLogMessage(QString("Final organization: %1 tracks created, %2 conflicts resolved, %.1f%% efficiency")
                 .arg(totalTracks).arg(conflictCount).arg(efficiencyScore));

    // Update organization timeline with final assignments
    updateOrganizationTimeline(finalAssignments);

    m_applyOrganizationButton->setEnabled(false);
    addLogMessage("Track organization applied successfully");
}

void MainWindow::onOrganizationCompleted(const QVariantList &assignments, const QVariantMap &stats)
{
    addLogMessage(QString("Track organization completed: %1 assignments").arg(assignments.size()));

    QString statsText = QString("Organization complete: %1 regions, %2 tracks, %3 conflicts")
                       .arg(stats.value("totalRegions").toInt())
                       .arg(stats.value("totalTracks").toInt())
                       .arg(stats.value("conflictCount").toInt());

    m_organizationStatsLabel->setText(statsText);
}

void MainWindow::openClassificationReview()
{
    if (!m_classificationEngine) {
        addLogMessage("Classification engine not available", true);
        return;
    }

    // Get current classification results from the table
    QVariantList results;
    for (int i = 0; i < m_classificationTable->rowCount(); ++i) {
        QVariantMap result;
        result["regionId"] = m_classificationTable->item(i, 0)->text();
        result["track"] = m_classificationTable->item(i, 1)->text();
        result["contentType"] = m_classificationTable->item(i, 2)->text();
        result["confidence"] = m_classificationTable->item(i, 3)->text().remove('%').toDouble() / 100.0;
        result["speakerId"] = m_classificationTable->item(i, 4)->text();
        result["needsReview"] = m_classificationTable->item(i, 5)->text() == "Yes";
        results.append(result);
    }

    if (results.isEmpty()) {
        addLogMessage("No classification results to review", true);
        return;
    }

    // Create review dialog if it doesn't exist
    if (!m_reviewDialog) {
        m_reviewDialog = new ClassificationReviewDialog(this);
        m_reviewDialog->setLMStudioClient(m_lmStudioClient);
        m_reviewDialog->setAudioFileManager(m_audioFileManager);
        m_reviewDialog->setAudioPlaybackManager(m_audioPlaybackManager);
        connect(m_reviewDialog, &ClassificationReviewDialog::classificationsUpdated,
                this, &MainWindow::onClassificationsUpdated);
    }

    // Set results and show dialog
    m_reviewDialog->setClassificationResults(results);

    // Show only low-confidence results by default
    m_reviewDialog->showLowConfidenceOnly(0.7);

    addLogMessage(QString("Opening classification review for %1 regions").arg(results.size()));
    m_reviewDialog->exec();
}

void MainWindow::onClassificationsUpdated(const QVariantList &updatedResults)
{
    addLogMessage(QString("Classification review completed: %1 regions updated").arg(updatedResults.size()));

    // Update the classification table with corrected results
    m_classificationTable->setRowCount(updatedResults.size());

    for (int i = 0; i < updatedResults.size(); ++i) {
        QVariantMap result = updatedResults[i].toMap();

        m_classificationTable->setItem(i, 0, new QTableWidgetItem(result.value("regionId").toString()));
        m_classificationTable->setItem(i, 1, new QTableWidgetItem(result.value("track").toString()));
        m_classificationTable->setItem(i, 2, new QTableWidgetItem(result.value("contentType").toString()));
        m_classificationTable->setItem(i, 3, new QTableWidgetItem(QString::number(result.value("confidence").toDouble() * 100, 'f', 1) + "%"));
        m_classificationTable->setItem(i, 4, new QTableWidgetItem(result.value("speakerId").toString()));

        bool needsReview = result.value("needsReview").toBool();
        QTableWidgetItem *reviewItem = new QTableWidgetItem(needsReview ? "Yes" : "No");
        if (needsReview) {
            reviewItem->setBackground(QColor(255, 200, 200));
        } else {
            reviewItem->setBackground(QColor(200, 255, 200)); // Light green for reviewed
        }
        m_classificationTable->setItem(i, 5, reviewItem);
    }

    // Update status
    int reviewedCount = 0;
    for (const QVariant &result : updatedResults) {
        if (!result.toMap().value("needsReview").toBool()) {
            reviewedCount++;
        }
    }

    m_classificationStatusLabel->setText(QString("Classification updated: %1 regions (%2 reviewed)")
                                        .arg(updatedResults.size()).arg(reviewedCount));

    // Enable save button since we have updated data
    m_saveClassificationButton->setEnabled(true);
}

void MainWindow::onLLMProviderChanged()
{
    bool useLocal = m_localLLMRadio->isChecked();

    // Update settings data and save immediately
    m_llmSettingsData.useLocalLLM = useLocal;
    saveSettings();

    QString provider = useLocal ? "Local LLM (LM Studio)" : "Online LLM";
    addLogMessage(QString("LLM provider changed to: %1").arg(provider));

    // Update LM Studio status if switching to local
    if (useLocal) {
        updateLMStudioStatus();
    }

    // Update LM Studio client configuration if available
    if (m_lmStudioClient) {
        QList<LMStudioClient::AIProvider> providers;

        if (useLocal) {
            providers = {LMStudioClient::LMStudio, LMStudioClient::OpenAI, LMStudioClient::Anthropic};
            addLogMessage("LLM provider preference set to: Local LLM (LM Studio) first");
        } else {
            providers = {LMStudioClient::OpenAI, LMStudioClient::Anthropic, LMStudioClient::LMStudio};
            addLogMessage("LLM provider preference set to: Online LLM first");
        }

        m_lmStudioClient->setProviderPreference(providers);
    }
}

void MainWindow::openLLMSettings()
{
    // Create a more comprehensive settings dialog
    QDialog *settingsDialog = new QDialog(this);
    settingsDialog->setWindowTitle("LLM Settings");
    settingsDialog->setModal(true);
    settingsDialog->resize(500, 400);

    QVBoxLayout *mainLayout = new QVBoxLayout(settingsDialog);

    // Provider Status Group
    QGroupBox *statusGroup = new QGroupBox("Provider Status");
    QFormLayout *statusLayout = new QFormLayout(statusGroup);

    QString currentProvider = m_localLLMRadio->isChecked() ? "Local LLM (LM Studio)" : "Online LLM";
    QString serverStatus = m_lmStudioClient ?
        (m_lmStudioClient->serverStatus() == LMStudioClient::Available ? "Available" : "Unavailable") : "Unknown";

    statusLayout->addRow("Current Provider:", new QLabel(currentProvider));
    statusLayout->addRow("LM Studio Status:", new QLabel(serverStatus));
    statusLayout->addRow("LM Studio URL:", new QLabel(m_llmSettingsData.lmStudioUrl));

    // API Keys Group
    QGroupBox *apiGroup = new QGroupBox("API Configuration");
    QFormLayout *apiLayout = new QFormLayout(apiGroup);

    QLineEdit *openaiKeyEdit = new QLineEdit;
    openaiKeyEdit->setEchoMode(QLineEdit::Password);
    openaiKeyEdit->setPlaceholderText("Enter OpenAI API key...");
    openaiKeyEdit->setText(m_llmSettingsData.openaiApiKey);

    QLineEdit *anthropicKeyEdit = new QLineEdit;
    anthropicKeyEdit->setEchoMode(QLineEdit::Password);
    anthropicKeyEdit->setPlaceholderText("Enter Anthropic API key...");
    anthropicKeyEdit->setText(m_llmSettingsData.anthropicApiKey);

    QLineEdit *lmStudioUrlEdit = new QLineEdit;
    lmStudioUrlEdit->setText(m_llmSettingsData.lmStudioUrl);
    lmStudioUrlEdit->setPlaceholderText("e.g., http://*************:5001 or http://localhost:1234");

    apiLayout->addRow("OpenAI API Key:", openaiKeyEdit);
    apiLayout->addRow("Anthropic API Key:", anthropicKeyEdit);
    apiLayout->addRow("LM Studio URL:", lmStudioUrlEdit);

    // Test Connections Group
    QGroupBox *testGroup = new QGroupBox("Connection Tests");
    QVBoxLayout *testLayout = new QVBoxLayout(testGroup);

    QPushButton *testLMStudioBtn = new QPushButton("Test LM Studio Connection");
    QPushButton *testOpenAIBtn = new QPushButton("Test OpenAI Connection");
    QPushButton *testAnthropicBtn = new QPushButton("Test Anthropic Connection");

    testLayout->addWidget(testLMStudioBtn);
    testLayout->addWidget(testOpenAIBtn);
    testLayout->addWidget(testAnthropicBtn);

    // Connect test buttons
    connect(testLMStudioBtn, &QPushButton::clicked, [this, lmStudioUrlEdit, testLMStudioBtn]() {
        QString testUrl = lmStudioUrlEdit->text().trimmed();
        if (testUrl.isEmpty()) {
            testUrl = "http://localhost:1234";
        }

        // Disable button during test
        testLMStudioBtn->setEnabled(false);
        testLMStudioBtn->setText("Testing...");

        // Perform actual HTTP connection test
        testLMStudioConnection(testUrl, [this, testLMStudioBtn](bool success, QString message) {
            // Re-enable button
            testLMStudioBtn->setEnabled(true);
            testLMStudioBtn->setText("Test LM Studio Connection");

            // Show result in a message box for immediate feedback
            if (success) {
                QMessageBox::information(this, "Connection Test", message);
            } else {
                QMessageBox::warning(this, "Connection Test", message);
            }
        });
    });

    connect(testOpenAIBtn, &QPushButton::clicked, [this]() {
        addLogMessage("OpenAI connection test not yet implemented");
    });

    connect(testAnthropicBtn, &QPushButton::clicked, [this]() {
        addLogMessage("Anthropic connection test not yet implemented");
    });

    // Dialog buttons
    QHBoxLayout *buttonLayout = new QHBoxLayout;
    QPushButton *saveBtn = new QPushButton("Save");
    QPushButton *cancelBtn = new QPushButton("Cancel");

    connect(saveBtn, &QPushButton::clicked, [settingsDialog, this, lmStudioUrlEdit, openaiKeyEdit, anthropicKeyEdit]() {
        QString newUrl = lmStudioUrlEdit->text().trimmed();

        // Validate URL format
        if (!newUrl.isEmpty() && !newUrl.startsWith("http://") && !newUrl.startsWith("https://")) {
            QMessageBox::warning(this, "Invalid URL", "LM Studio URL should start with http:// or https://");
            return;
        }

        // Update settings data
        m_llmSettingsData.lmStudioUrl = newUrl.isEmpty() ? "http://*************:5001" : newUrl;
        m_llmSettingsData.openaiApiKey = openaiKeyEdit->text();
        m_llmSettingsData.anthropicApiKey = anthropicKeyEdit->text();

        // Save to persistent storage
        saveSettings();

        // Update LLM provider selection
        m_llmSettingsData.useLocalLLM = m_localLLMRadio->isChecked();

        // Log changes
        addLogMessage(QString("LM Studio URL updated to: %1").arg(m_llmSettingsData.lmStudioUrl));

        if (!m_llmSettingsData.openaiApiKey.isEmpty()) {
            addLogMessage("OpenAI API key updated");
        }

        if (!m_llmSettingsData.anthropicApiKey.isEmpty()) {
            addLogMessage("Anthropic API key updated");
        }

        addLogMessage("LLM settings saved and persisted");

        // Update LM Studio client URL immediately
        if (m_lmStudioClient) {
            QUrl serverUrl(m_llmSettingsData.lmStudioUrl);
            m_lmStudioClient->setServerUrl(serverUrl);
        }

        // Update LM Studio status
        updateLMStudioStatus();

        settingsDialog->accept();
    });

    connect(cancelBtn, &QPushButton::clicked, settingsDialog, &QDialog::reject);

    buttonLayout->addStretch();
    buttonLayout->addWidget(saveBtn);
    buttonLayout->addWidget(cancelBtn);

    // Add to main layout
    mainLayout->addWidget(statusGroup);
    mainLayout->addWidget(apiGroup);
    mainLayout->addWidget(testGroup);
    mainLayout->addLayout(buttonLayout);

    settingsDialog->exec();
    delete settingsDialog;

    addLogMessage("LLM settings dialog accessed");
}

QString MainWindow::formatTimecode(double timeInSeconds) const
{
    // Use professional SMPTE timecode formatting
    return TimecodeUtils::formatTimecode(timeInSeconds,
                                       TimecodeUtils::Format::FRAMES,
                                       TimecodeUtils::FrameRate::FPS_25);
}

double MainWindow::parseTimeString(const QString &timeString) const
{
    if (timeString.isEmpty()) {
        return 0.0;
    }

    // Use professional timecode parsing
    double result = TimecodeUtils::parseTimecode(timeString, TimecodeUtils::FrameRate::FPS_25);
    return result >= 0 ? result : 0.0;
}

void MainWindow::browseExportPath()
{
    QString defaultPath = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation);
    QString selectedPath = QFileDialog::getExistingDirectory(this, "Select Export Directory", defaultPath);

    if (!selectedPath.isEmpty()) {
        m_exportPathLineEdit->setText(selectedPath);
        updateExportButtonState();
        addLogMessage(QString("Export directory set to: %1").arg(selectedPath));
    }
}

void MainWindow::updateExportButtonState()
{
    bool hasDirectory = !m_exportPathLineEdit->text().isEmpty();
    bool hasFileName = !m_exportFileNameLineEdit->text().trimmed().isEmpty();
    m_startExportButton->setEnabled(hasDirectory && hasFileName);
}

void MainWindow::startExport()
{
    QString exportDirectory = m_exportPathLineEdit->text();
    QString fileName = m_exportFileNameLineEdit->text().trimmed();

    if (exportDirectory.isEmpty()) {
        addLogMessage("Please select an export directory", true);
        return;
    }

    if (fileName.isEmpty()) {
        addLogMessage("Please enter a file name", true);
        return;
    }

    if (!m_aafExporter) {
        addLogMessage("AAF exporter not available", true);
        return;
    }

    // Determine export format and file extension
    AAFExporter::ExportFormat format = AAFExporter::AAF;
    QString formatName = "AAF";
    QString fileExtension = ".aaf";

    if (m_omfExportRadio->isChecked()) {
        format = AAFExporter::OMF;
        formatName = "OMF";
        fileExtension = ".omf";
    } else if (m_xmlExportRadio->isChecked()) {
        format = AAFExporter::XML;
        formatName = "XML";
        fileExtension = ".xml";
    } else if (m_csvExportRadio->isChecked()) {
        format = AAFExporter::CSV;
        formatName = "CSV";
        fileExtension = ".csv";
    } else if (m_jsonExportRadio->isChecked()) {
        format = AAFExporter::JSON;
        formatName = "JSON";
        fileExtension = ".json";
    }

    // Construct full export path
    QString fullFileName = fileName;
    if (!fullFileName.endsWith(fileExtension, Qt::CaseInsensitive)) {
        fullFileName += fileExtension;
    }
    QString exportPath = QDir(exportDirectory).absoluteFilePath(fullFileName);

    // Prepare export options
    AAFExporter::ExportOptions options;
    options.format = format;
    options.includeAudio = m_includeAudioCheckBox->isChecked();
    options.includeMetadata = m_includeMetadataCheckBox->isChecked();
    options.includeTimecode = m_includeTimecodeCheckBox->isChecked();
    options.includeClassification = m_includeClassificationCheckBox->isChecked();
    options.includeOrganization = m_includeOrganizationCheckBox->isChecked();
    options.audioQuality = m_audioQualityComboBox->currentText();
    options.sampleRate = m_sampleRateComboBox->currentText();
    options.bitDepth = m_bitDepthComboBox->currentText();

    // Get regions and tracks for export
    QVariantList regions;
    QVariantList tracks;

    // Get regions from classification table if available
    if (m_classificationTable->rowCount() > 0) {
        for (int i = 0; i < m_classificationTable->rowCount(); ++i) {
            QVariantMap region;
            region["id"] = QString("region_%1").arg(i);
            region["name"] = m_classificationTable->item(i, 0)->text();
            region["track"] = m_classificationTable->item(i, 1)->text();
            region["contentType"] = m_classificationTable->item(i, 2)->text();
            region["confidence"] = m_classificationTable->item(i, 3)->text().remove('%').toDouble() / 100.0;
            region["speakerId"] = m_classificationTable->item(i, 4)->text();

            // Parse timecode from table
            QString timeStr = m_classificationTable->item(i, 5)->text();
            // Convert HH:MM:SS.mmm to seconds (simplified)
            QStringList timeParts = timeStr.split(':');
            if (timeParts.size() >= 3) {
                double hours = timeParts[0].toDouble();
                double minutes = timeParts[1].toDouble();
                QStringList secParts = timeParts[2].split('.');
                double seconds = secParts[0].toDouble();
                double milliseconds = secParts.size() > 1 ? secParts[1].toDouble() / 1000.0 : 0.0;
                region["startTime"] = hours * 3600 + minutes * 60 + seconds + milliseconds;
            }

            // Parse duration
            QString durationStr = m_classificationTable->item(i, 6)->text().remove('s');
            region["duration"] = durationStr.toDouble();

            regions.append(region);
        }
    } else if (m_aafReader && m_aafReader->isLoaded()) {
        // Fallback to AAF reader regions
        regions = m_aafReader->getAllRegions();
    }

    // Get tracks from organization table if available
    if (m_organizationTable->rowCount() > 0) {
        QSet<QString> trackNames;
        for (int i = 0; i < m_organizationTable->rowCount(); ++i) {
            QString trackName = m_organizationTable->item(i, 2)->text(); // Assigned Track column
            trackNames.insert(trackName);
        }

        int trackIndex = 0;
        for (const QString &trackName : trackNames) {
            QVariantMap track;
            track["index"] = trackIndex++;
            track["name"] = trackName;
            track["type"] = "Audio";
            tracks.append(track);
        }
    } else {
        // Create default tracks
        for (int i = 0; i < 8; ++i) {
            QVariantMap track;
            track["index"] = i;
            track["name"] = QString("Track %1").arg(i + 1);
            track["type"] = "Audio";
            tracks.append(track);
        }
    }

    if (regions.isEmpty()) {
        addLogMessage("No regions available for export", true);
        return;
    }

    // Apply time range filtering for export
    QVariantList filteredRegions = regions;
    if (m_exportTimeRangeData.mode == ExportAnalysisRange && m_timeRangeData.isValid()) {
        // Use analysis time range
        filteredRegions.clear();
        for (const QVariant &regionVariant : regions) {
            QVariantMap region = regionVariant.toMap();
            double regionStart = region.value("startTime").toDouble();
            double regionDuration = region.value("duration").toDouble();
            double regionEnd = regionStart + regionDuration;

            // Include regions that overlap with the analysis time range
            if (regionEnd > m_timeRangeData.startTime && regionStart < m_timeRangeData.endTime) {
                filteredRegions.append(regionVariant);
            }
        }

        QString timeRangeStr = QString("%1 to %2")
            .arg(formatTimecode(m_timeRangeData.startTime))
            .arg(formatTimecode(m_timeRangeData.endTime));
        addLogMessage(QString("Exporting analysis time range %1: %2 of %3 regions")
                     .arg(timeRangeStr).arg(filteredRegions.size()).arg(regions.size()));

    } else if (m_exportTimeRangeData.mode == ExportCustomRange && m_exportTimeRangeData.isCustomRangeValid()) {
        // Use custom time range
        filteredRegions.clear();
        for (const QVariant &regionVariant : regions) {
            QVariantMap region = regionVariant.toMap();
            double regionStart = region.value("startTime").toDouble();
            double regionDuration = region.value("duration").toDouble();
            double regionEnd = regionStart + regionDuration;

            // Include regions that overlap with the custom time range
            if (regionEnd > m_exportTimeRangeData.startTime && regionStart < m_exportTimeRangeData.endTime) {
                filteredRegions.append(regionVariant);
            }
        }

        QString timeRangeStr = QString("%1 to %2")
            .arg(formatTimecode(m_exportTimeRangeData.startTime))
            .arg(formatTimecode(m_exportTimeRangeData.endTime));
        addLogMessage(QString("Exporting custom time range %1: %2 of %3 regions")
                     .arg(timeRangeStr).arg(filteredRegions.size()).arg(regions.size()));
    } else {
        addLogMessage("Exporting full AAF file");
    }

    if (filteredRegions.isEmpty()) {
        addLogMessage("No regions found in the specified export time range", true);
        return;
    }

    // Use filtered regions for export
    regions = filteredRegions;

    addLogMessage(QString("Starting %1 export to: %2").arg(formatName).arg(exportPath));
    addLogMessage(QString("Exporting %1 regions across %2 tracks").arg(regions.size()).arg(tracks.size()));

    // Connect export signals
    connect(m_aafExporter, &AAFExporter::progressChanged, this, [this](double progress, const QString &message) {
        m_exportProgressBar->setValue(static_cast<int>(progress * 100));
        m_exportStatusLabel->setText(message);
    });

    connect(m_aafExporter, &AAFExporter::exportCompleted, this, [this](const QString &outputPath) {
        m_startExportButton->setEnabled(true);
        m_exportProgressBar->setVisible(false);
        m_exportStatusLabel->setText("Export completed successfully");
        addLogMessage(QString("Export completed: %1").arg(outputPath));

        // Disconnect signals
        disconnect(m_aafExporter, &AAFExporter::progressChanged, this, nullptr);
        disconnect(m_aafExporter, &AAFExporter::exportCompleted, this, nullptr);
        disconnect(m_aafExporter, &AAFExporter::exportFailed, this, nullptr);
    });

    connect(m_aafExporter, &AAFExporter::exportFailed, this, [this](const QString &error) {
        m_startExportButton->setEnabled(true);
        m_exportProgressBar->setVisible(false);
        m_exportStatusLabel->setText("Export failed");
        addLogMessage(QString("Export failed: %1").arg(error), true);

        // Disconnect signals
        disconnect(m_aafExporter, &AAFExporter::progressChanged, this, nullptr);
        disconnect(m_aafExporter, &AAFExporter::exportCompleted, this, nullptr);
        disconnect(m_aafExporter, &AAFExporter::exportFailed, this, nullptr);
    });

    // Show progress and start export
    m_exportProgressBar->setVisible(true);
    m_exportProgressBar->setValue(0);
    m_exportStatusLabel->setText("Starting export...");
    m_startExportButton->setEnabled(false);

    // Start the actual export
    bool exportStarted = m_aafExporter->exportRegions(regions, tracks, exportPath, options);

    if (!exportStarted) {
        m_startExportButton->setEnabled(true);
        m_exportProgressBar->setVisible(false);
        m_exportStatusLabel->setText("Failed to start export");
        addLogMessage("Failed to start export", true);
    }
}

void MainWindow::onExportFormatChanged()
{
    // Enable/disable audio quality options based on format
    bool isAudioFormat = m_aafExportRadio->isChecked() || m_omfExportRadio->isChecked() || m_xmlExportRadio->isChecked();
    m_exportQualityGroup->setEnabled(isAudioFormat);
    m_includeAudioCheckBox->setEnabled(isAudioFormat);

    if (!isAudioFormat) {
        m_includeAudioCheckBox->setChecked(false);
    }

    QString format = "AAF";
    if (m_omfExportRadio->isChecked()) format = "OMF";
    else if (m_xmlExportRadio->isChecked()) format = "XML";
    else if (m_csvExportRadio->isChecked()) format = "CSV";
    else if (m_jsonExportRadio->isChecked()) format = "JSON";

    addLogMessage(QString("Export format changed to: %1").arg(format));
}

void MainWindow::openAnalysisSettings()
{
    if (!m_analysisSettings) {
        addLogMessage("Analysis settings not available");
        return;
    }

    AnalysisSettingsDialog dialog(m_analysisSettings, this);
    if (dialog.exec() == QDialog::Accepted) {
        addLogMessage("Analysis settings updated");
    }
}

void MainWindow::loadSettings()
{
    qDebug() << "loadSettings: Starting settings load";
    QSettings settings("WAAFer", "WAAFer");
    qDebug() << "loadSettings: QSettings object created";

    // Load LLM settings
    qDebug() << "loadSettings: Loading LLM settings";
    m_llmSettingsData.lmStudioUrl = settings.value("llm/lmStudioUrl", "http://*************:5001").toString();
    qDebug() << "loadSettings: LM Studio URL loaded:" << m_llmSettingsData.lmStudioUrl;
    m_llmSettingsData.openaiApiKey = settings.value("llm/openaiApiKey", "").toString();
    qDebug() << "loadSettings: OpenAI API key loaded";
    m_llmSettingsData.anthropicApiKey = settings.value("llm/anthropicApiKey", "").toString();
    qDebug() << "loadSettings: Anthropic API key loaded";
    m_llmSettingsData.useLocalLLM = settings.value("llm/useLocalLLM", true).toBool();
    qDebug() << "loadSettings: Use local LLM setting loaded:" << m_llmSettingsData.useLocalLLM;

    // Load analysis settings
    qDebug() << "loadSettings: Loading analysis settings";
    m_settingsData.quality = settings.value("analysis/quality", "Balanced").toString();
    qDebug() << "loadSettings: Quality setting loaded:" << m_settingsData.quality;
    m_settingsData.chunkSkipFactor = settings.value("analysis/chunkSkipFactor", 1).toInt();
    qDebug() << "loadSettings: Chunk skip factor loaded:" << m_settingsData.chunkSkipFactor;
    m_settingsData.maxConcurrent = settings.value("analysis/maxConcurrent", 2).toInt();
    qDebug() << "loadSettings: Max concurrent loaded:" << m_settingsData.maxConcurrent;
    m_settingsData.fastMode = settings.value("analysis/fastMode", false).toBool();
    qDebug() << "loadSettings: Fast mode loaded:" << m_settingsData.fastMode;
    m_settingsData.confidenceThreshold = settings.value("analysis/confidenceThreshold", 0.6).toDouble();
    qDebug() << "loadSettings: Confidence threshold loaded:" << m_settingsData.confidenceThreshold;
    m_settingsData.speakerDiarization = settings.value("analysis/speakerDiarization", true).toBool();
    qDebug() << "loadSettings: Speaker diarization loaded:" << m_settingsData.speakerDiarization;
    m_settingsData.musicDetection = settings.value("analysis/musicDetection", true).toBool();
    qDebug() << "loadSettings: Music detection loaded:" << m_settingsData.musicDetection;
    m_settingsData.sfxDetection = settings.value("analysis/sfxDetection", true).toBool();
    qDebug() << "loadSettings: SFX detection loaded:" << m_settingsData.sfxDetection;
    m_settingsData.disableMockData = settings.value("analysis/disableMockData", false).toBool();
    qDebug() << "loadSettings: Disable mock data loaded:" << m_settingsData.disableMockData;

    qDebug() << "loadSettings: Settings loading completed successfully";
    // Note: Don't call addLogMessage here as UI is not yet initialized
    // The log message will be added later when UI is ready
}

void MainWindow::saveSettings()
{
    QSettings settings("WAAFer", "WAAFer");

    // Save LLM settings
    settings.setValue("llm/lmStudioUrl", m_llmSettingsData.lmStudioUrl);
    settings.setValue("llm/openaiApiKey", m_llmSettingsData.openaiApiKey);
    settings.setValue("llm/anthropicApiKey", m_llmSettingsData.anthropicApiKey);
    settings.setValue("llm/useLocalLLM", m_llmSettingsData.useLocalLLM);

    // Save analysis settings
    settings.setValue("analysis/quality", m_settingsData.quality);
    settings.setValue("analysis/chunkSkipFactor", m_settingsData.chunkSkipFactor);
    settings.setValue("analysis/maxConcurrent", m_settingsData.maxConcurrent);
    settings.setValue("analysis/fastMode", m_settingsData.fastMode);
    settings.setValue("analysis/confidenceThreshold", m_settingsData.confidenceThreshold);
    settings.setValue("analysis/speakerDiarization", m_settingsData.speakerDiarization);
    settings.setValue("analysis/musicDetection", m_settingsData.musicDetection);
    settings.setValue("analysis/sfxDetection", m_settingsData.sfxDetection);
    settings.setValue("analysis/disableMockData", m_settingsData.disableMockData);

    settings.sync();
    addLogMessage("Settings saved to persistent storage");
}

void MainWindow::testLMStudioConnection(const QString &url, std::function<void(bool, QString)> callback)
{
    if (!m_networkManager) {
        callback(false, "Network manager not available");
        return;
    }

    // Validate URL format
    QUrl testUrl(url);
    if (!testUrl.isValid() || testUrl.scheme().isEmpty()) {
        callback(false, "Invalid URL format");
        return;
    }

    // Create request to test LM Studio endpoint
    // LM Studio typically exposes a /v1/models endpoint for health checks
    QString testEndpoint = url;
    if (!testEndpoint.endsWith("/")) {
        testEndpoint += "/";
    }
    testEndpoint += "v1/models";

    QUrl requestUrl(testEndpoint);
    QNetworkRequest request(requestUrl);
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");
    request.setRawHeader("User-Agent", "WAAFer/1.0");

    // Set timeout
    request.setTransferTimeout(5000); // 5 seconds

    addLogMessage(QString("Testing connection to: %1").arg(testEndpoint));

    QNetworkReply *reply = m_networkManager->get(request);

    // Handle the response
    connect(reply, &QNetworkReply::finished, [this, reply, callback, url]() {
        bool success = false;
        QString message;

        if (reply->error() == QNetworkReply::NoError) {
            int statusCode = reply->attribute(QNetworkRequest::HttpStatusCodeAttribute).toInt();
            if (statusCode == 200) {
                success = true;
                message = QString("✅ Successfully connected to LM Studio at %1").arg(url);
                addLogMessage(message);
            } else {
                message = QString("❌ LM Studio responded with status code %1").arg(statusCode);
                addLogMessage(message, true);
            }
        } else {
            QString errorString = reply->errorString();
            if (reply->error() == QNetworkReply::ConnectionRefusedError) {
                message = QString("❌ Connection refused - LM Studio server not running at %1").arg(url);
            } else if (reply->error() == QNetworkReply::TimeoutError) {
                message = QString("❌ Connection timeout - LM Studio server not responding at %1").arg(url);
            } else if (reply->error() == QNetworkReply::HostNotFoundError) {
                message = QString("❌ Host not found - Check if %1 is accessible").arg(url);
            } else {
                message = QString("❌ Connection failed: %1").arg(errorString);
            }
            addLogMessage(message, true);
        }

        callback(success, message);
        reply->deleteLater();
    });

    // Handle timeout
    connect(reply, &QNetworkReply::errorOccurred, [this, reply, callback](QNetworkReply::NetworkError error) {
        if (error == QNetworkReply::TimeoutError) {
            addLogMessage("❌ Connection test timed out", true);
        }
    });
}

void MainWindow::updateLMStudioStatus()
{
    // Test the current LM Studio URL and update status
    testLMStudioConnection(m_llmSettingsData.lmStudioUrl, [this](bool success, QString message) {
        // Update the LLM provider status if needed
        if (m_lmStudioClient) {
            // This would update the client's internal status
            addLogMessage(QString("LM Studio status updated: %1").arg(success ? "Available" : "Unavailable"));
        }
    });
}

// Phase 5: Timeline Widget Implementation
QWidget* MainWindow::createTimelineTab()
{
    qDebug() << "createTimelineTab: Starting timeline tab creation";
    qDebug() << "createTimelineTab: Creating TimelineWidget";
    m_timelineWidget = new TimelineWidget;
    qDebug() << "createTimelineTab: TimelineWidget created successfully";

    // Set core components for timeline
    qDebug() << "createTimelineTab: Setting core components";
    if (m_aafReader) {
        qDebug() << "createTimelineTab: Setting AAF reader";
        m_timelineWidget->setAAFReader(m_aafReader);
        qDebug() << "createTimelineTab: AAF reader set";
    }
    if (m_audioFileManager) {
        qDebug() << "createTimelineTab: Setting audio file manager";
        m_timelineWidget->setAudioFileManager(m_audioFileManager);
        qDebug() << "createTimelineTab: Audio file manager set";
    }
    if (m_audioPlaybackManager) {
        qDebug() << "createTimelineTab: Setting audio playback manager";
        m_timelineWidget->setAudioPlaybackManager(m_audioPlaybackManager);
        qDebug() << "createTimelineTab: Audio playback manager set";
    }

    // Connect timeline signals to main window
    connect(m_timelineWidget, &TimelineWidget::regionSelected,
            this, &MainWindow::onTimelineRegionSelected);
    connect(m_timelineWidget, &TimelineWidget::regionUpdated,
            this, &MainWindow::onTimelineRegionUpdated);
    connect(m_timelineWidget, &TimelineWidget::playbackRequested,
            this, &MainWindow::onTimelinePlaybackRequested);
    connect(m_timelineWidget, &TimelineWidget::timeRangeSelectionChanged,
            this, &MainWindow::onTimelineRangeSelected);

    // Note: Analysis range signal connections moved to setupUI() after all tabs are created

    // Create wrapper widget with timeline controls
    qDebug() << "createTimelineTab: Creating wrapper widget and layout";
    QWidget *timelineTab = new QWidget;
    QVBoxLayout *layout = new QVBoxLayout(timelineTab);
    layout->setContentsMargins(5, 5, 5, 5);
    layout->setSpacing(5);
    qDebug() << "createTimelineTab: Wrapper widget and layout created";

    // Time range selection is handled in the Classification tab

    // Add the timeline widget directly to the tab
    qDebug() << "createTimelineTab: Adding timeline widget to main layout";
    layout->addWidget(m_timelineWidget, 1); // Give it stretch factor to fill space
    qDebug() << "createTimelineTab: Timeline tab creation completed successfully";

    return timelineTab;
}



void MainWindow::onTimelineRegionSelected(const QString &regionId)
{
    addLogMessage(QString("Timeline region selected: %1").arg(regionId));

    // Update classification table selection if available
    if (m_classificationTable) {
        for (int row = 0; row < m_classificationTable->rowCount(); ++row) {
            QTableWidgetItem *item = m_classificationTable->item(row, 0);
            if (item && item->data(Qt::UserRole).toString() == regionId) {
                m_classificationTable->selectRow(row);
                break;
            }
        }
    }

    // Synchronize with organization timeline - bidirectional sync
    if (m_organizationTimelineWidget) {
        // Find and select the corresponding region in the organization timeline
        emit m_organizationTimelineWidget->regionSelected(regionId);
    }

    // Update organization table selection if available
    if (m_organizationTable) {
        for (int row = 0; row < m_organizationTable->rowCount(); ++row) {
            QTableWidgetItem *nameItem = m_organizationTable->item(row, 0);
            if (nameItem && nameItem->text() == regionId) {
                m_organizationTable->selectRow(row);
                m_organizationTable->scrollToItem(nameItem);
                break;
            }
        }
    }
}

void MainWindow::onTimelineRegionUpdated(const QString &regionId, const QVariantMap &metadata)
{
    addLogMessage(QString("Timeline region updated: %1").arg(regionId));

    // Update classification results if available
    if (m_classificationTable) {
        for (int row = 0; row < m_classificationTable->rowCount(); ++row) {
            QTableWidgetItem *item = m_classificationTable->item(row, 0);
            if (item && item->data(Qt::UserRole).toString() == regionId) {
                // Update table row with new metadata
                if (m_classificationTable->item(row, 1)) {
                    m_classificationTable->item(row, 1)->setText(metadata.value("name").toString());
                }
                if (m_classificationTable->item(row, 2)) {
                    m_classificationTable->item(row, 2)->setText(metadata.value("contentType").toString());
                }
                if (m_classificationTable->item(row, 4)) {
                    m_classificationTable->item(row, 4)->setText(metadata.value("speakerId").toString());
                }
                break;
            }
        }
    }

    // Synchronize with organization timeline
    if (m_organizationTimelineWidget) {
        emit m_organizationTimelineWidget->regionUpdated(regionId, metadata);
    }

    // Update organization table if available
    if (m_organizationTable) {
        for (int row = 0; row < m_organizationTable->rowCount(); ++row) {
            QTableWidgetItem *nameItem = m_organizationTable->item(row, 0);
            if (nameItem && nameItem->text() == regionId) {
                // Update relevant columns with new metadata
                if (metadata.contains("assignedTrack") && m_organizationTable->item(row, 2)) {
                    m_organizationTable->item(row, 2)->setText(metadata.value("assignedTrack").toString());
                }
                if (metadata.contains("contentType") && m_organizationTable->item(row, 3)) {
                    m_organizationTable->item(row, 3)->setText(metadata.value("contentType").toString());
                }
                if (metadata.contains("speakerId") && m_organizationTable->item(row, 4)) {
                    m_organizationTable->item(row, 4)->setText(metadata.value("speakerId").toString());
                }
                break;
            }
        }
    }
}

void MainWindow::onTimelinePlaybackRequested(const QString &regionId)
{
    addLogMessage(QString("Timeline playback requested for region: %1").arg(regionId));

    // This would integrate with audio playback system
    // For now, just log the request
    if (m_audioFileManager) {
        // Future implementation: m_audioFileManager->playRegion(regionId);
        addLogMessage("Audio playback not yet implemented");
    }
}

void MainWindow::onTimelineRangeSelected(double startTime, double endTime)
{
    // Format the times using SMPTE timecode
    QString startTimeStr = TimecodeUtils::formatTimecode(startTime,
                                                       TimecodeUtils::Format::FRAMES,
                                                       TimecodeUtils::FrameRate::FPS_25);
    QString endTimeStr = TimecodeUtils::formatTimecode(endTime,
                                                     TimecodeUtils::Format::FRAMES,
                                                     TimecodeUtils::FrameRate::FPS_25);

    // Uncheck "Analyze Full AAF" to enable time range inputs
    m_analyzeFullAAFCheckBox->setChecked(false);

    // Set the time range values
    m_startTimeEdit->setText(startTimeStr);
    m_endTimeEdit->setText(endTimeStr);

    // Trigger update to validate and apply the range
    onTimeRangeChanged();

    addLogMessage(QString("Time range selected from timeline: %1 to %2").arg(startTimeStr, endTimeStr));
}

void MainWindow::onTimelineAnalysisRangeChanged(bool fullAAF, double startTime, double endTime)
{
    qDebug() << "MainWindow::onTimelineAnalysisRangeChanged called with fullAAF:" << fullAAF << "startTime:" << startTime << "endTime:" << endTime;

    // Safety check - ensure all widgets are initialized
    if (!m_analyzeFullAAFCheckBox || !m_startTimeEdit || !m_endTimeEdit) {
        qDebug() << "MainWindow::onTimelineAnalysisRangeChanged - Classification tab controls not yet initialized, deferring synchronization";
        // Store the values for later synchronization when Classification tab is ready
        m_pendingTimelineSync = true;
        m_pendingFullAAF = fullAAF;
        m_pendingStartTime = startTime;
        m_pendingEndTime = endTime;
        return;
    }

    qDebug() << "MainWindow::onTimelineAnalysisRangeChanged - widgets are valid, proceeding with synchronization";

    // Temporarily block signals to prevent recursive calls
    m_analyzeFullAAFCheckBox->blockSignals(true);
    m_startTimeEdit->blockSignals(true);
    m_endTimeEdit->blockSignals(true);

    qDebug() << "MainWindow::onTimelineAnalysisRangeChanged - signals blocked";

    // Synchronize the Classification tab analysis range controls with Timeline tab
    m_analyzeFullAAFCheckBox->setChecked(fullAAF);

    qDebug() << "MainWindow::onTimelineAnalysisRangeChanged - checkbox updated";

    if (!fullAAF && startTime >= 0 && endTime > 0) {
        // Format times as SMPTE timecode and update Classification tab controls
        QString startTimeStr = TimecodeUtils::formatTimecode(startTime, TimecodeUtils::Format::FRAMES, TimecodeUtils::FrameRate::FPS_25);
        QString endTimeStr = TimecodeUtils::formatTimecode(endTime, TimecodeUtils::Format::FRAMES, TimecodeUtils::FrameRate::FPS_25);

        qDebug() << "MainWindow::onTimelineAnalysisRangeChanged - formatted times:" << startTimeStr << endTimeStr;

        m_startTimeEdit->setText(startTimeStr);
        m_endTimeEdit->setText(endTimeStr);
    } else {
        m_startTimeEdit->clear();
        m_endTimeEdit->clear();
    }

    qDebug() << "MainWindow::onTimelineAnalysisRangeChanged - time edits updated";

    // Restore signal connections
    m_analyzeFullAAFCheckBox->blockSignals(false);
    m_startTimeEdit->blockSignals(false);
    m_endTimeEdit->blockSignals(false);

    qDebug() << "MainWindow::onTimelineAnalysisRangeChanged - signals unblocked";

    // Trigger the main window's time range change handler to update internal state
    qDebug() << "MainWindow::onTimelineAnalysisRangeChanged - calling onTimeRangeChanged";
    onTimeRangeChanged();
    qDebug() << "MainWindow::onTimelineAnalysisRangeChanged - onTimeRangeChanged completed";

    addLogMessage(QString("Analysis range updated from Timeline tab: Full AAF=%1, Start=%2, End=%3")
                  .arg(fullAAF ? "true" : "false")
                  .arg(startTime)
                  .arg(endTime));

    qDebug() << "MainWindow::onTimelineAnalysisRangeChanged - completed successfully";
}

void MainWindow::onOrganizationTimelineRegionSelected(const QString &regionId)
{
    addLogMessage(QString("Organization timeline region selected: %1").arg(regionId));

    // Synchronize with main timeline - select the same region
    if (m_timelineWidget) {
        // Find and select the corresponding region in the main timeline
        // This creates bidirectional synchronization
        emit m_timelineWidget->regionSelected(regionId);
    }

    // Update classification table selection if the region exists there
    if (m_classificationTable) {
        for (int row = 0; row < m_classificationTable->rowCount(); ++row) {
            QTableWidgetItem *idItem = m_classificationTable->item(row, 0);
            if (idItem && idItem->text() == regionId) {
                m_classificationTable->selectRow(row);
                m_classificationTable->scrollToItem(idItem);
                break;
            }
        }
    }

    // Update organization table selection if the region exists there
    if (m_organizationTable) {
        for (int row = 0; row < m_organizationTable->rowCount(); ++row) {
            QTableWidgetItem *nameItem = m_organizationTable->item(row, 0);
            if (nameItem && nameItem->text() == regionId) {
                m_organizationTable->selectRow(row);
                m_organizationTable->scrollToItem(nameItem);
                break;
            }
        }
    }
}

void MainWindow::onOrganizationTimelineRegionUpdated(const QString &regionId, const QVariantMap &metadata)
{
    addLogMessage(QString("Organization timeline region updated: %1").arg(regionId));

    // Synchronize updates back to main timeline
    if (m_timelineWidget) {
        emit m_timelineWidget->regionUpdated(regionId, metadata);
    }

    // Update organization table if the region exists there
    if (m_organizationTable) {
        for (int row = 0; row < m_organizationTable->rowCount(); ++row) {
            QTableWidgetItem *nameItem = m_organizationTable->item(row, 0);
            if (nameItem && nameItem->text() == regionId) {
                // Update relevant columns with new metadata
                if (metadata.contains("assignedTrack")) {
                    QTableWidgetItem *trackItem = m_organizationTable->item(row, 2);
                    if (trackItem) {
                        trackItem->setText(metadata.value("assignedTrack").toString());
                    }
                }
                if (metadata.contains("contentType")) {
                    QTableWidgetItem *contentItem = m_organizationTable->item(row, 3);
                    if (contentItem) {
                        contentItem->setText(metadata.value("contentType").toString());
                    }
                }
                break;
            }
        }
    }
}

void MainWindow::onOrganizationTableSelectionChanged()
{
    if (!m_organizationTable) return;

    QList<QTableWidgetItem*> selectedItems = m_organizationTable->selectedItems();
    if (selectedItems.isEmpty()) return;

    // Get the region name from the first column of the selected row
    int selectedRow = selectedItems.first()->row();
    QTableWidgetItem *regionNameItem = m_organizationTable->item(selectedRow, 0);
    if (!regionNameItem) return;

    QString regionId = regionNameItem->text();
    addLogMessage(QString("Organization table selection changed: %1").arg(regionId));

    // Synchronize with main timeline
    if (m_timelineWidget) {
        emit m_timelineWidget->regionSelected(regionId);
    }

    // Synchronize with organization timeline
    if (m_organizationTimelineWidget) {
        emit m_organizationTimelineWidget->regionSelected(regionId);
    }

    // Synchronize with classification table
    if (m_classificationTable) {
        for (int row = 0; row < m_classificationTable->rowCount(); ++row) {
            QTableWidgetItem *item = m_classificationTable->item(row, 0);
            if (item && item->data(Qt::UserRole).toString() == regionId) {
                m_classificationTable->selectRow(row);
                m_classificationTable->scrollToItem(item);
                break;
            }
        }
    }
}

void MainWindow::onClassificationTableSelectionChanged()
{
    if (!m_classificationTable) return;

    QList<QTableWidgetItem*> selectedItems = m_classificationTable->selectedItems();
    if (selectedItems.isEmpty()) return;

    // Get the region ID from the first column of the selected row
    int selectedRow = selectedItems.first()->row();
    QTableWidgetItem *regionItem = m_classificationTable->item(selectedRow, 0);
    if (!regionItem) return;

    QString regionId = regionItem->data(Qt::UserRole).toString();
    if (regionId.isEmpty()) {
        // Fallback to text if UserRole is not set
        regionId = regionItem->text();
    }

    addLogMessage(QString("Classification table selection changed: %1").arg(regionId));

    // Synchronize with main timeline
    if (m_timelineWidget) {
        emit m_timelineWidget->regionSelected(regionId);
    }

    // Synchronize with organization timeline
    if (m_organizationTimelineWidget) {
        emit m_organizationTimelineWidget->regionSelected(regionId);
    }

    // Synchronize with organization table
    if (m_organizationTable) {
        for (int row = 0; row < m_organizationTable->rowCount(); ++row) {
            QTableWidgetItem *nameItem = m_organizationTable->item(row, 0);
            if (nameItem && nameItem->text() == regionId) {
                m_organizationTable->selectRow(row);
                m_organizationTable->scrollToItem(nameItem);
                break;
            }
        }
    }
}

void MainWindow::openPresetManagement()
{
    if (!m_trackOrganizer) {
        addLogMessage("Track organizer not available", true);
        return;
    }

    PresetManagementDialog dialog(m_trackOrganizer, this);

    if (dialog.exec() == QDialog::Accepted) {
        // Refresh template combo box with any new presets
        QString currentTemplate = m_templateComboBox->currentText();
        m_templateComboBox->clear();

        QStringList templates = m_trackOrganizer->availableTemplates();
        m_templateComboBox->addItems(templates);

        // Restore selection if still available
        int index = m_templateComboBox->findText(currentTemplate);
        if (index >= 0) {
            m_templateComboBox->setCurrentIndex(index);
        }

        addLogMessage("Preset management completed");
    }
}

// Custom sorting functions removed - using Qt's built-in sorting instead
// This eliminates crashes and provides consistent behavior with Classification tab

void MainWindow::onTimeRangeChanged()
{
    bool fullAAF = m_analyzeFullAAFCheckBox->isChecked();

    // Enable/disable time input controls
    m_startTimeEdit->setEnabled(!fullAAF);
    m_endTimeEdit->setEnabled(!fullAAF);
    m_setFromTimelineButton->setEnabled(!fullAAF);
    m_clearTimeRangeButton->setEnabled(!fullAAF && (!m_startTimeEdit->text().isEmpty() || !m_endTimeEdit->text().isEmpty()));

    // Update time range data
    m_timeRangeData.analyzeFullAAF = fullAAF;

    if (!fullAAF) {
        // Parse time inputs
        m_timeRangeData.startTime = parseTimeString(m_startTimeEdit->text());
        m_timeRangeData.endTime = parseTimeString(m_endTimeEdit->text());

        // Validate time range
        if (m_timeRangeData.endTime > 0 && m_timeRangeData.startTime >= m_timeRangeData.endTime) {
            m_startTimeEdit->setStyleSheet("QLineEdit { background-color: #ffcccc; }");
            m_endTimeEdit->setStyleSheet("QLineEdit { background-color: #ffcccc; }");
        } else {
            m_startTimeEdit->setStyleSheet("");
            m_endTimeEdit->setStyleSheet("");
        }
    }

    qDebug() << "Time range changed: Full AAF=" << fullAAF
             << "Start=" << m_timeRangeData.startTime
             << "End=" << m_timeRangeData.endTime;

    // Update timeline highlight
    if (m_timelineWidget) {
        if (fullAAF) {
            m_timelineWidget->clearTimeRangeHighlight();
        } else if (m_timeRangeData.isValid()) {
            m_timelineWidget->setTimeRangeHighlight(m_timeRangeData.startTime, m_timeRangeData.endTime, true);
        }
    }
}

void MainWindow::setTimeRangeFromTimeline()
{
    if (m_timelineWidget) {
        QPair<double, double> selectedRange = m_timelineWidget->getSelectedTimeRange();

        if (selectedRange.first != selectedRange.second) {
            // We have a valid time range selection
            QString startTimeStr = TimecodeUtils::formatTimecode(selectedRange.first,
                                                               TimecodeUtils::Format::FRAMES,
                                                               TimecodeUtils::FrameRate::FPS_25);
            QString endTimeStr = TimecodeUtils::formatTimecode(selectedRange.second,
                                                             TimecodeUtils::Format::FRAMES,
                                                             TimecodeUtils::FrameRate::FPS_25);

            // Uncheck "Analyze Full AAF" to enable time range inputs
            m_analyzeFullAAFCheckBox->setChecked(false);

            // Set the time range values
            m_startTimeEdit->setText(startTimeStr);
            m_endTimeEdit->setText(endTimeStr);

            // Trigger update
            onTimeRangeChanged();

            addLogMessage(QString("Time range set from timeline: %1 to %2").arg(startTimeStr, endTimeStr));
        } else {
            addLogMessage("No time range selected in timeline. Use Shift+drag on the timeline ruler to select a range.");
        }
    }
}

void MainWindow::clearTimeRange()
{
    m_startTimeEdit->clear();
    m_endTimeEdit->clear();
    m_timeRangeData.startTime = 0.0;
    m_timeRangeData.endTime = 0.0;
    onTimeRangeChanged();
}

void MainWindow::onExportTimeRangeModeChanged()
{
    // Update export time range mode
    if (m_exportFullAAFRadio->isChecked()) {
        m_exportTimeRangeData.mode = ExportFullAAF;
        m_exportStartTimeEdit->setEnabled(false);
        m_exportEndTimeEdit->setEnabled(false);
    } else if (m_exportAnalysisRangeRadio->isChecked()) {
        m_exportTimeRangeData.mode = ExportAnalysisRange;
        m_exportStartTimeEdit->setEnabled(false);
        m_exportEndTimeEdit->setEnabled(false);

        // Update display with analysis range
        if (m_timeRangeData.isValid()) {
            m_exportStartTimeEdit->setText(formatTimecode(m_timeRangeData.startTime));
            m_exportEndTimeEdit->setText(formatTimecode(m_timeRangeData.endTime));
        } else {
            m_exportStartTimeEdit->setText("No analysis range set");
            m_exportEndTimeEdit->setText("No analysis range set");
        }
    } else if (m_exportCustomRangeRadio->isChecked()) {
        m_exportTimeRangeData.mode = ExportCustomRange;
        m_exportStartTimeEdit->setEnabled(true);
        m_exportEndTimeEdit->setEnabled(true);

        // Clear display text if it was showing analysis range info
        if (m_exportStartTimeEdit->text() == "No analysis range set") {
            m_exportStartTimeEdit->clear();
            m_exportEndTimeEdit->clear();
        }
    }

    // Update custom range data if in custom mode
    if (m_exportTimeRangeData.mode == ExportCustomRange) {
        m_exportTimeRangeData.startTime = parseTimeString(m_exportStartTimeEdit->text());
        m_exportTimeRangeData.endTime = parseTimeString(m_exportEndTimeEdit->text());

        // Validate custom time range
        if (m_exportTimeRangeData.endTime > 0 && m_exportTimeRangeData.startTime >= m_exportTimeRangeData.endTime) {
            m_exportStartTimeEdit->setStyleSheet("QLineEdit { background-color: #ffcccc; }");
            m_exportEndTimeEdit->setStyleSheet("QLineEdit { background-color: #ffcccc; }");
        } else {
            m_exportStartTimeEdit->setStyleSheet("");
            m_exportEndTimeEdit->setStyleSheet("");
        }
    }

    // Update export file name to reflect time range
    updateExportFileName();

    qDebug() << "Export time range mode changed:" << m_exportTimeRangeData.mode;
}

void MainWindow::updateExportFileName()
{
    if (!m_exportFileNameLineEdit) return;

    QString baseName = m_exportFileNameLineEdit->text();

    // Remove any existing time range suffix
    QRegularExpression timeRangeRegex("_\\d{2}-\\d{2}-\\d{2}_to_\\d{2}-\\d{2}-\\d{2}");
    baseName = baseName.remove(timeRangeRegex);

    // Remove file extension for processing
    QString extension;
    int dotIndex = baseName.lastIndexOf('.');
    if (dotIndex > 0) {
        extension = baseName.mid(dotIndex);
        baseName = baseName.left(dotIndex);
    }

    // Add time range suffix if not exporting full AAF
    QString newFileName = baseName;
    if (m_exportTimeRangeData.mode == ExportAnalysisRange && m_timeRangeData.isValid()) {
        QString startStr = formatTimecode(m_timeRangeData.startTime).replace(":", "-").replace(".", "-");
        QString endStr = formatTimecode(m_timeRangeData.endTime).replace(":", "-").replace(".", "-");
        newFileName += QString("_%1_to_%2").arg(startStr).arg(endStr);
    } else if (m_exportTimeRangeData.mode == ExportCustomRange && m_exportTimeRangeData.isCustomRangeValid()) {
        QString startStr = formatTimecode(m_exportTimeRangeData.startTime).replace(":", "-").replace(".", "-");
        QString endStr = formatTimecode(m_exportTimeRangeData.endTime).replace(":", "-").replace(".", "-");
        newFileName += QString("_%1_to_%2").arg(startStr).arg(endStr);
    }

    // Add back extension
    newFileName += extension;

    m_exportFileNameLineEdit->setText(newFileName);
}

void MainWindow::saveClassificationData()
{
    if (!m_classificationTable || m_classificationTable->rowCount() == 0) {
        QMessageBox::warning(this, "No Data", "No classification data available to save.");
        return;
    }

    QString fileName = QFileDialog::getSaveFileName(this,
        "Save Classification Data",
        "classification_data.json",
        "JSON Files (*.json)");

    if (fileName.isEmpty()) {
        return;
    }

    // Collect classification data from table
    QJsonArray classificationsArray;
    for (int row = 0; row < m_classificationTable->rowCount(); ++row) {
        QJsonObject classification;

        // Get data from table items
        QTableWidgetItem *regionItem = m_classificationTable->item(row, 0);
        QTableWidgetItem *trackItem = m_classificationTable->item(row, 1);
        QTableWidgetItem *contentItem = m_classificationTable->item(row, 2);
        QTableWidgetItem *confidenceItem = m_classificationTable->item(row, 3);
        QTableWidgetItem *speakerItem = m_classificationTable->item(row, 4);
        QTableWidgetItem *startTimeItem = m_classificationTable->item(row, 5);
        QTableWidgetItem *durationItem = m_classificationTable->item(row, 6);
        QTableWidgetItem *lengthItem = m_classificationTable->item(row, 7);
        QTableWidgetItem *reviewItem = m_classificationTable->item(row, 8);

        if (regionItem) {
            classification["regionName"] = regionItem->text();
            // Store region ID from UserRole if available
            QString regionId = regionItem->data(Qt::UserRole).toString();
            if (!regionId.isEmpty()) {
                classification["regionId"] = regionId;
            }
        }
        if (trackItem) classification["trackName"] = trackItem->text();
        if (contentItem) classification["contentType"] = contentItem->text();
        if (confidenceItem) {
            QString confText = confidenceItem->text().remove('%');
            classification["confidence"] = confText.toDouble() / 100.0;
        }
        if (speakerItem) classification["speakerId"] = speakerItem->text();
        if (startTimeItem) classification["startTime"] = startTimeItem->text();
        if (durationItem) classification["duration"] = durationItem->text();
        if (lengthItem) classification["length"] = lengthItem->text();
        if (reviewItem) classification["needsReview"] = (reviewItem->text() == "Yes");

        classificationsArray.append(classification);
    }

    // Create export data
    QJsonObject exportData;
    exportData["version"] = "1.0";
    exportData["exportTime"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    exportData["totalRegions"] = m_classificationTable->rowCount();
    exportData["classifications"] = classificationsArray;

    // Add AAF file info if available
    if (m_aafReader && m_aafReader->isLoaded()) {
        exportData["aafFile"] = m_aafReader->currentFile();
    }

    QJsonDocument doc(exportData);

    QFile file(fileName);
    if (file.open(QIODevice::WriteOnly)) {
        file.write(doc.toJson());
        addLogMessage(QString("Classification data saved to: %1").arg(fileName));
        QMessageBox::information(this, "Save Complete",
            QString("Classification data saved successfully.\n%1 regions exported.").arg(m_classificationTable->rowCount()));
    } else {
        addLogMessage(QString("Failed to save classification data: %1").arg(file.errorString()), true);
        QMessageBox::warning(this, "Save Failed",
            QString("Failed to save file: %1").arg(file.errorString()));
    }
}

void MainWindow::loadClassificationData()
{
    QString fileName = QFileDialog::getOpenFileName(this,
        "Load Classification Data",
        "",
        "JSON Files (*.json)");

    if (fileName.isEmpty()) {
        return;
    }

    QFile file(fileName);
    if (!file.open(QIODevice::ReadOnly)) {
        addLogMessage(QString("Failed to open classification file: %1").arg(file.errorString()), true);
        QMessageBox::warning(this, "Load Failed",
            QString("Failed to open file: %1").arg(file.errorString()));
        return;
    }

    QByteArray data = file.readAll();
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);

    if (error.error != QJsonParseError::NoError) {
        addLogMessage(QString("Failed to parse classification JSON: %1").arg(error.errorString()), true);
        QMessageBox::warning(this, "Load Failed",
            QString("Invalid JSON format: %1").arg(error.errorString()));
        return;
    }

    QJsonObject rootObj = doc.object();
    if (!rootObj.contains("classifications")) {
        QMessageBox::warning(this, "Load Failed", "File does not contain classification data.");
        return;
    }

    QJsonArray classificationsArray = rootObj["classifications"].toArray();

    // Clear existing table
    m_classificationTable->setRowCount(0);

    // Load classifications into table
    m_classificationTable->setRowCount(classificationsArray.size());

    for (int i = 0; i < classificationsArray.size(); ++i) {
        QJsonObject classification = classificationsArray[i].toObject();

        // Region Name
        QTableWidgetItem *regionItem = new QTableWidgetItem(classification["regionName"].toString());
        if (classification.contains("regionId")) {
            regionItem->setData(Qt::UserRole, classification["regionId"].toString());
        }
        m_classificationTable->setItem(i, 0, regionItem);

        // Track Name
        m_classificationTable->setItem(i, 1, new QTableWidgetItem(classification["trackName"].toString()));

        // Content Type
        QString contentType = classification["contentType"].toString();
        QTableWidgetItem *contentItem = new QTableWidgetItem(contentType);
        if (contentType.contains("❌") || contentType.contains("MOCK") || contentType.contains("Unknown")) {
            contentItem->setForeground(QColor(255, 150, 150)); // Light red for mock data
            contentItem->setToolTip("Mock classification result");
        }
        m_classificationTable->setItem(i, 2, contentItem);

        // Confidence
        double confidence = classification["confidence"].toDouble();
        QString confidenceText = QString::number(confidence * 100, 'f', 1) + "%";
        QTableWidgetItem *confidenceItem = new QTableWidgetItem(confidenceText);
        if (confidence < 0.7) {
            confidenceItem->setForeground(QColor(255, 200, 100)); // Orange for low confidence
        }
        m_classificationTable->setItem(i, 3, confidenceItem);

        // Speaker
        m_classificationTable->setItem(i, 4, new QTableWidgetItem(classification["speakerId"].toString()));

        // Start Time
        m_classificationTable->setItem(i, 5, new QTableWidgetItem(classification["startTime"].toString()));

        // Duration
        m_classificationTable->setItem(i, 6, new QTableWidgetItem(classification["duration"].toString()));

        // Length
        m_classificationTable->setItem(i, 7, new QTableWidgetItem(classification["length"].toString()));

        // Needs Review
        bool needsReview = classification["needsReview"].toBool();
        QTableWidgetItem *reviewItem = new QTableWidgetItem(needsReview ? "Yes" : "No");
        if (needsReview) {
            reviewItem->setForeground(QColor(255, 200, 100)); // Orange for needs review
        }
        m_classificationTable->setItem(i, 8, reviewItem);
    }

    // Enable buttons
    m_exportClassificationButton->setEnabled(true);
    m_reviewClassificationButton->setEnabled(true);
    m_saveClassificationButton->setEnabled(true);

    addLogMessage(QString("Classification data loaded from: %1").arg(fileName));
    addLogMessage(QString("Loaded %1 classification results").arg(classificationsArray.size()));

    QMessageBox::information(this, "Load Complete",
        QString("Classification data loaded successfully.\n%1 regions imported.").arg(classificationsArray.size()));
}


