#pragma once

#include <QWidget>
#include <QScrollArea>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QPushButton>
#include <QSlider>
#include <QTimer>
#include <QPainter>
#include <QMouseEvent>
#include <QWheelEvent>
#include <QMenu>
#include <QAction>
#include <QSplitter>
#include <QListWidget>
#include <QGroupBox>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QComboBox>
#include <QCheckBox>
#include <QLineEdit>
#include <QTextEdit>
#include <QToolButton>
#include <QVariantList>
#include <QVariantMap>
#include <QFrame>
#include <QButtonGroup>
#include <QToolBar>
#include <QCache>
#include <QMutex>
#include <QMutexLocker>
#include <QSharedPointer>

// Forward declarations
class AAFReader;
class AudioFileManager;
class AudioPlaybackManager;

/**
 * @brief Shared waveform cache for all TimelineWidget instances
 */
class SharedWaveformCache {
public:
    static SharedWaveformCache& instance();

    void insertWaveformData(const QString& regionId, const struct WaveformData& data);
    struct WaveformData* getWaveformData(const QString& regionId);
    void clear();
    int size() const;

private:
    SharedWaveformCache() = default;
    ~SharedWaveformCache() = default;
    SharedWaveformCache(const SharedWaveformCache&) = delete;
    SharedWaveformCache& operator=(const SharedWaveformCache&) = delete;

    mutable QMutex m_mutex;
    QCache<QString, struct WaveformData> m_cache{1000}; // Cache up to 1000 waveforms
};

/**
 * @brief Waveform data structure for audio visualization
 */
struct WaveformData {
    QVector<float> peaks;      // Peak values for waveform display
    QVector<float> rms;        // RMS values for waveform display
    double sampleRate = 44100.0;
    double duration = 0.0;
    int samplesPerPixel = 1024;
    bool isCalculating = false;
    bool isComplete = false;

    WaveformData() = default;
    WaveformData(int numSamples) : peaks(numSamples), rms(numSamples) {}
};

/**
 * @brief Represents an audio region in the timeline
 */
struct TimelineRegion {
    QString id;
    QString name;
    QString alternativeName;  // For region name toggle (Source A vs Source B)
    QString trackName;
    double startTime;
    double duration;
    QString contentType;
    QString speakerId;
    double confidence;
    QColor color;
    bool selected = false;
    bool muted = false;
    QString audioFilePath;
    QVariantMap metadata;
    int channelNumber = 0;    // For stereo channel identification
    QString stereoGroupId;    // For grouping stereo pairs
};

/**
 * @brief Represents a track in the timeline
 */
struct TimelineTrack {
    QString name;           // Display name with numbering (e.g., "1. Dialogue")
    QString originalName;   // Original AAF track name for matching
    QString type;
    int trackNumber = -1;   // AAF track number/index
    bool visible = true;
    bool muted = false;
    bool solo = false;
    bool selected = true;   // Track selection for analysis/export
    QColor color;
    QList<TimelineRegion> regions;
    int height = 60;
};

/**
 * @brief Professional timeline ruler with SMPTE timecode display
 */
class TimelineRuler : public QWidget
{
    Q_OBJECT

public:
    explicit TimelineRuler(QWidget *parent = nullptr);

    void setDuration(double duration);
    void setZoom(double zoom);
    void setPlayheadPosition(double position);
    void setFrameRate(double frameRate);
    void setTimecodeFormat(int format); // 0=HH:MM:SS:FF, 1=HH:MM:SS.mmm

    double pixelsToTime(int pixels) const;
    int timeToPixels(double time) const;

protected:
    void paintEvent(QPaintEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void wheelEvent(QWheelEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;

signals:
    void playheadMoved(double position);
    void zoomRequested(double factor);

private:
    double m_duration = 300.0;
    double m_zoom = 1.0;
    double m_playheadPosition = 0.0;
    double m_frameRate = 25.0;
    int m_timecodeFormat = 0; // 0=frames, 1=milliseconds
    int m_rulerHeight = 40;

    // Time range selection state
    bool m_dragging = false;

    QString formatTimecode(double seconds) const;
    void drawTimeMarkers(QPainter &painter);
    void drawPlayhead(QPainter &painter);
};

/**
 * @brief Compact track header with integrated controls
 */
class TrackHeader : public QWidget
{
    Q_OBJECT

public:
    explicit TrackHeader(const TimelineTrack &track, QWidget *parent = nullptr);

    void updateTrack(const TimelineTrack &track);
    QString getTrackName() const;
    bool isSelected() const;
    void setSelected(bool selected);
    void setHeight(int height);

protected:
    void paintEvent(QPaintEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;

signals:
    void muteToggled(const QString &trackName, bool muted);
    void soloToggled(const QString &trackName, bool solo);
    void visibilityToggled(const QString &trackName, bool visible);
    void trackRenamed(const QString &oldName, const QString &newName);
    void selectionToggled(const QString &trackName, bool selected);
    void trackHeightChanged(const QString &trackName, int height);

private slots:
    void onMuteClicked();
    void onSoloClicked();
    void onVisibilityClicked();
    void onSelectionClicked();
    void onNameEditFinished();

private:
    void setupLayout();
    void updateButtonStates();

    TimelineTrack m_track;
    QCheckBox *m_selectionCheckBox;
    QToolButton *m_muteButton;
    QToolButton *m_soloButton;
    QToolButton *m_visibilityButton;
    QLineEdit *m_nameEdit;
    QLabel *m_trackNumberLabel;

    static const int HEADER_WIDTH = 200;
    static const int BUTTON_SIZE = 20;
};

/**
 * @brief Enhanced track content with improved region visualization
 */
class TrackContent : public QWidget
{
    Q_OBJECT

public:
    explicit TrackContent(const TimelineTrack &track, QWidget *parent = nullptr);

    void setTrack(const TimelineTrack &track);
    void setZoom(double zoom);
    void setDuration(double duration);
    void setPlayheadPosition(double position);
    void setTimelineWidget(class TimelineWidget *timelineWidget);
    void setTimeRangeHighlight(double startTime, double endTime, bool enabled);
    void setShowWaveforms(bool show);
    void setShowRegionNames(bool show);
    void setShowConfidence(bool show);

    double pixelsToTime(int pixels) const;
    int timeToPixels(double time) const;

protected:
    void paintEvent(QPaintEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void contextMenuEvent(QContextMenuEvent *event) override;
    void wheelEvent(QWheelEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;

signals:
    void regionSelected(const QString &regionId);
    void regionMoved(const QString &regionId, double newStartTime);
    void regionContextMenu(const QString &regionId, const QPoint &position);
    void zoomRequested(double factor);

private:
    TimelineRegion* getRegionAt(const QPoint &pos);
    void drawRegion(QPainter &painter, const TimelineRegion &region);
    void drawTimeRangeHighlight(QPainter &painter);
    void drawPlayhead(QPainter &painter);
    void drawWaveform(QPainter &painter, const TimelineRegion &region, const QRect &rect);

    TimelineTrack m_track;
    double m_zoom = 1.0;
    double m_duration = 300.0;
    double m_playheadPosition = 0.0;
    class TimelineWidget *m_timelineWidget = nullptr;

    // Time range highlighting
    bool m_timeRangeHighlightEnabled = false;
    double m_timeRangeStartTime = 0.0;
    double m_timeRangeEndTime = 0.0;

    // Mouse interaction
    bool m_dragging = false;
    TimelineRegion *m_draggedRegion = nullptr;
    QPoint m_dragStartPos;
    double m_dragStartTime;

    // Visual enhancements
    bool m_showWaveforms = false;  // Default to OFF as requested
    bool m_showRegionNames = true;
    bool m_showConfidence = true;
};

/**
 * @brief Professional transport controls with timecode display
 */
class TransportControls : public QWidget
{
    Q_OBJECT

public:
    explicit TransportControls(QWidget *parent = nullptr);

    void setDuration(double duration);
    void setPosition(double position);
    void setFrameRate(double frameRate);
    void setTimecodeFormat(int format);
    bool isPlaying() const { return m_playing; }

public slots:
    void play();
    void pause();
    void stop();
    void setPlayheadPosition(double position);

signals:
    void playRequested();
    void pauseRequested();
    void stopRequested();
    void positionChanged(double position);
    void frameRateChanged(double frameRate);
    void timecodeFormatChanged(int format);

private slots:
    void onPlayClicked();
    void onPauseClicked();
    void onStopClicked();
    void onPositionSliderChanged(int value);
    void onFrameRateChanged();
    void onTimecodeFormatChanged();

private:
    void setupLayout();
    void updateTimeDisplay();
    QString formatTimecode(double seconds) const;

    QToolButton *m_playButton;
    QToolButton *m_pauseButton;
    QToolButton *m_stopButton;
    QSlider *m_positionSlider;
    QLabel *m_timeLabel;
    QLabel *m_durationLabel;
    QComboBox *m_frameRateCombo;
    QComboBox *m_timecodeFormatCombo;

    bool m_playing = false;
    double m_duration = 300.0;
    double m_position = 0.0;
    double m_frameRate = 25.0;
    int m_timecodeFormat = 0;
};

/**
 * @brief Timeline control toolbar with zoom and navigation
 */
class TimelineToolbar : public QToolBar
{
    Q_OBJECT

public:
    explicit TimelineToolbar(QWidget *parent = nullptr);

    // Getter for toggle action
    QAction* getToggleRegionNameSourceAction() const { return m_toggleRegionNameSourceAction; }

signals:
    void zoomInRequested();
    void zoomOutRequested();
    void fitToWindowRequested();
    void showWaveformsToggled(bool show);
    void showRegionNamesToggled(bool show);
    void showConfidenceToggled(bool show);
    void trackSelectionRequested(bool selectAll);

private slots:
    void onZoomIn();
    void onZoomOut();
    void onFitToWindow();
    void onSelectAllTracks();
    void onDeselectAllTracks();
    void onToggleRegionNameSource();

private:
    void setupActions();

    QAction *m_zoomInAction;
    QAction *m_zoomOutAction;
    QAction *m_fitToWindowAction;
    QAction *m_showWaveformsAction;
    QAction *m_showRegionNamesAction;
    QAction *m_toggleRegionNameSourceAction;  // Toggle between Source A and Source B
    QAction *m_showConfidenceAction;
    QAction *m_selectAllTracksAction;
    QAction *m_deselectAllTracksAction;
};

/**
 * @brief Compact region metadata panel
 */
class RegionMetadataPanel : public QWidget
{
    Q_OBJECT

public:
    explicit RegionMetadataPanel(QWidget *parent = nullptr);

    void setRegion(const TimelineRegion &region);
    void clearRegion();
    void setCompactMode(bool compact);

signals:
    void regionUpdated(const QString &regionId, const QVariantMap &metadata);

private slots:
    void onMetadataChanged();

private:
    void setupLayout();
    void updateLayout();

    QString m_currentRegionId;
    bool m_compactMode = true;

    QLineEdit *m_nameEdit;
    QLineEdit *m_speakerEdit;
    QComboBox *m_contentTypeCombo;
    QDoubleSpinBox *m_startTimeSpin;
    QDoubleSpinBox *m_durationSpin;
    QDoubleSpinBox *m_confidenceSpin;
    QTextEdit *m_notesEdit;
    QCheckBox *m_mutedCheck;

    QWidget *m_basicInfoWidget;
    QWidget *m_advancedInfoWidget;
};

/**
 * @brief Redesigned timeline widget with professional layout and enhanced functionality
 */
class TimelineWidget : public QWidget
{
    Q_OBJECT

public:
    explicit TimelineWidget(QWidget *parent = nullptr);

    void setAAFReader(AAFReader *reader);
    void setAudioFileManager(AudioFileManager *manager);
    void setAudioPlaybackManager(AudioPlaybackManager *manager);
    void loadTimelineData(const QVariantList &regions, const QVariantList &tracks);
    void updateRegionClassification(const QString &regionId, const QVariantMap &classification);
    void refreshTimeline();
    void forceRefresh();

    bool preserveOriginalTiming() const;

    // Track and region selection
    QStringList getSelectedTrackNames() const;
    QStringList getSelectedRegionIds() const;
    QVariantList getSelectedRegions() const;
    void setTrackSelection(const QString &trackName, bool selected);
    void setAllTracksSelected(bool selected);

    // Time range highlighting
    void setTimeRangeHighlight(double startTime, double endTime, bool enabled = true);
    void clearTimeRangeHighlight();
    bool isTimeRangeHighlightEnabled() const { return m_timeRangeHighlightEnabled; }
    double getTimeRangeStartTime() const { return m_timeRangeStartTime; }
    double getTimeRangeEndTime() const { return m_timeRangeEndTime; }

    // Time range selection
    void startTimeRangeSelection(double startTime);
    void updateTimeRangeSelection(double endTime);
    void finishTimeRangeSelection();
    void cancelTimeRangeSelection();
    bool isTimeRangeSelectionActive() const { return m_timeRangeSelectionActive; }
    QPair<double, double> getSelectedTimeRange() const;

    // Timeline properties
    void setFrameRate(double frameRate);
    void setTimecodeFormat(int format);
    double getZoom() const { return m_zoom; }
    double getDuration() const { return m_duration; }

public slots:
    void zoomIn();
    void zoomOut();
    void fitToWindow();
    void playPause();
    void stop();
    void onShowWaveformsToggled(bool show);
    void onShowRegionNamesToggled(bool show);
    void onToggleRegionNameSource();  // Toggle between Source A and Source B
    void onShowConfidenceToggled(bool show);

    // Waveform data access
    WaveformData* getWaveformData(const QString &regionId);

    // Getter for region name source state
    bool useAlternativeRegionNames() const { return m_useAlternativeRegionNames; }

protected:
    void resizeEvent(QResizeEvent *event) override;

signals:
    void regionSelected(const QString &regionId);
    void regionUpdated(const QString &regionId, const QVariantMap &metadata);
    void playbackRequested(const QString &regionId);
    void trackSelectionChanged(const QStringList &selectedTracks);
    void timeRangeSelectionChanged(double startTime, double endTime);

    // Analysis range signals
    void analysisRangeChanged(bool fullAAF, double startTime, double endTime);
    void setTimeRangeFromTimelineRequested();
    void clearTimeRangeRequested();

private slots:
    void onPlayheadMoved(double position);
    void onRegionSelected(const QString &regionId);
    void onTrackMuteToggled(const QString &trackName, bool muted);
    void onTrackSoloToggled(const QString &trackName, bool solo);
    void onTrackSelectionToggled(const QString &trackName, bool selected);
    void onRegionContextMenu(const QString &regionId, const QPoint &position);
    void onZoomRequested(double factor);

    // Transport control slots
    void onTransportPlayRequested();
    void onTransportPauseRequested();
    void onTransportStopRequested();
    void onTransportPositionChanged(double position);
    void onFrameRateChanged(double frameRate);
    void onTimecodeFormatChanged(int format);

    // Toolbar slots
    void onToolbarZoomIn();
    void onToolbarZoomOut();
    void onToolbarFitToWindow();
    void onToolbarTrackSelection(bool selectAll);

    // Audio playback slots
    void onAudioPlaybackPositionChanged(double position);
    void onAudioPlaybackDurationChanged(double duration);
    void onAudioPlaybackStateChanged(bool isPlaying);

    // Audio device management
    void initializeAudioComponents();

    // Scroll synchronization
    void onHorizontalScrollChanged(int value);
    void onVerticalScrollChanged(int value);

private:
    void setupUI();
    void setupLayout();
    void setupConnections();
    void updateTrackDisplay();
    void createTracksFromRegions(const QVariantList &regions);
    void updateZoom();
    void updateTimelineSize();
    void syncTrackSelection();
    QColor getColorForContentType(const QString &contentType) const;

    // UI Components - Main Layout
    QVBoxLayout *m_mainLayout;
    TimelineToolbar *m_toolbar;
    TransportControls *m_transportControls;

    // Timeline Display
    QSplitter *m_timelineSplitter;
    QScrollArea *m_timelineScrollArea;
    QWidget *m_timelineContainer;
    QVBoxLayout *m_timelineLayout;
    TimelineRuler *m_ruler;

    // Track Display
    QWidget *m_tracksWidget;
    QHBoxLayout *m_tracksLayout;
    QWidget *m_trackHeadersWidget;
    QWidget *m_trackContentsWidget;
    QVBoxLayout *m_trackHeadersLayout;
    QVBoxLayout *m_trackContentsLayout;

    // Metadata Panel
    RegionMetadataPanel *m_metadataPanel;

    // Analysis Range Controls
    QGroupBox *m_analysisRangeGroup;
    QCheckBox *m_analyzeFullAAFCheckBox;
    QLineEdit *m_startTimeEdit;
    QLineEdit *m_endTimeEdit;
    QPushButton *m_setFromTimelineButton;
    QPushButton *m_clearTimeRangeButton;

    // Settings
    QCheckBox *m_preserveTimingCheckBox;

    // Track Components
    QList<TrackHeader*> m_trackHeaders;
    QList<TrackContent*> m_trackContents;

    // Time range highlighting
    bool m_timeRangeHighlightEnabled = false;
    double m_timeRangeStartTime = 0.0;
    double m_timeRangeEndTime = 0.0;

    // Data Management
    QList<TimelineTrack> m_tracks;
    QHash<QString, TimelineRegion> m_regions;
    AAFReader *m_aafReader = nullptr;
    AudioFileManager *m_audioFileManager = nullptr;
    AudioPlaybackManager *m_audioPlaybackManager = nullptr;

    // Timeline State
    double m_zoom = 1.0;
    double m_duration = 300.0;
    double m_playheadPosition = 0.0;
    double m_frameRate = 25.0;
    int m_timecodeFormat = 0; // 0=frames, 1=milliseconds
    QString m_selectedRegionId;

    // Time range selection state
    bool m_timeRangeSelectionActive = false;
    double m_timeRangeSelectionStart = 0.0;
    double m_timeRangeSelectionEnd = 0.0;

    // Display Options
    bool m_showWaveforms = false;  // Default to OFF as requested
    bool m_showRegionNames = true;
    bool m_showConfidence = true;
    bool m_useAlternativeRegionNames = false;  // Toggle between Source A and Source B

    // Waveform Data Management
    // Note: Using SharedWaveformCache::instance() instead of local cache
    QMap<QString, bool> m_waveformCalculationStatus;  // Track calculation status

    // Waveform calculation methods
    void calculateWaveformData(const QString &regionId, const QString &audioFilePath);
    void onWaveformCalculationComplete(const QString &regionId, const WaveformData &data);
    WaveformData calculateWaveformFromFile(const QString &audioFilePath);

public:
    // Layout Constants
    static const int TRACK_HEIGHT = 60;
    static const int RULER_HEIGHT = 40;
    static const int TOOLBAR_HEIGHT = 32;
    static const int TRANSPORT_HEIGHT = 40;
    static const int HEADER_WIDTH = 200;
};
