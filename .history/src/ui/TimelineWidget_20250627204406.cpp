#include "TimelineWidget.h"
#include "../core/AAFReader.h"
#include "../audio/AudioFileManager.h"
#include "../audio/AudioPlaybackManager.h"
#include "../utils/TimecodeUtils.h"

#include <QApplication>
#include <QSplitter>
#include <QScrollArea>
#include <QScrollBar>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QFormLayout>
#include <QLabel>
#include <QPushButton>
#include <QToolButton>
#include <QSlider>
#include <QTimer>
#include <QPainter>
#include <QMouseEvent>
#include <QWheelEvent>
#include <QMenu>
#include <QAction>
#include <QListWidget>
#include <QGroupBox>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QComboBox>
#include <QCheckBox>
#include <QLineEdit>
#include <QTextEdit>
#include <QContextMenuEvent>
#include <QToolBar>
#include <QFrame>
#include <QButtonGroup>
#include <QDebug>
#include <QFuture>
#include <QFutureWatcher>
#include <QtConcurrent/QtConcurrent>
#include <QFileInfo>
#include <QRandomGenerator>

// ============================================================================
// SharedWaveformCache Implementation
// ============================================================================

SharedWaveformCache& SharedWaveformCache::instance() {
    static SharedWaveformCache instance;
    return instance;
}

void SharedWaveformCache::insertWaveformData(const QString& regionId, const WaveformData& data) {
    QMutexLocker locker(&m_mutex);
    WaveformData* cachedData = new WaveformData(data);
    cachedData->isComplete = true;
    cachedData->isCalculating = false;
    m_cache.insert(regionId, cachedData);
    qDebug() << "SharedWaveformCache::insertWaveformData - stored waveform for region" << regionId
             << "cache size now:" << m_cache.size();
}

WaveformData* SharedWaveformCache::getWaveformData(const QString& regionId) {
    QMutexLocker locker(&m_mutex);
    WaveformData* result = m_cache.object(regionId);
    qDebug() << "SharedWaveformCache::getWaveformData - region" << regionId
             << "cache size:" << m_cache.size()
             << "result:" << (result != nullptr ? "found" : "not found");
    return result;
}

void SharedWaveformCache::clear() {
    QMutexLocker locker(&m_mutex);
    m_cache.clear();
    qDebug() << "SharedWaveformCache::clear - cache cleared";
}

int SharedWaveformCache::size() const {
    QMutexLocker locker(&m_mutex);
    return m_cache.size();
}

// ============================================================================
// TimelineRuler Implementation
// ============================================================================
TimelineRuler::TimelineRuler(QWidget *parent)
    : QWidget(parent)
{
    setFixedHeight(m_rulerHeight);
    setMouseTracking(true);
    setMinimumWidth(800);
}

void TimelineRuler::setDuration(double duration)
{
    m_duration = duration;
    update();
}

void TimelineRuler::setZoom(double zoom)
{
    m_zoom = qMax(0.1, qMin(10.0, zoom)); // Clamp zoom between 0.1x and 10x
    update();
}

void TimelineRuler::setPlayheadPosition(double position)
{
    m_playheadPosition = position;
    update();
}

void TimelineRuler::setFrameRate(double frameRate)
{
    m_frameRate = frameRate;
    update();
}

void TimelineRuler::setTimecodeFormat(int format)
{
    m_timecodeFormat = format;
    update();
}

double TimelineRuler::pixelsToTime(int pixels) const
{
    return (pixels / m_zoom) / 100.0; // 100 pixels per second at zoom 1.0
}

int TimelineRuler::timeToPixels(double time) const
{
    return static_cast<int>(time * 100.0 * m_zoom);
}

QString TimelineRuler::formatTimecode(double seconds) const
{
    if (m_timecodeFormat == 0) {
        // Frame-based timecode (HH:MM:SS:FF)
        TimecodeUtils::FrameRate frameRate = TimecodeUtils::FrameRate::FPS_25;
        if (qAbs(m_frameRate - 23.976) < 0.1) frameRate = TimecodeUtils::FrameRate::FPS_23_976;
        else if (qAbs(m_frameRate - 24.0) < 0.1) frameRate = TimecodeUtils::FrameRate::FPS_24;
        else if (qAbs(m_frameRate - 25.0) < 0.1) frameRate = TimecodeUtils::FrameRate::FPS_25;
        else if (qAbs(m_frameRate - 29.97) < 0.1) frameRate = TimecodeUtils::FrameRate::FPS_29_97;
        else if (qAbs(m_frameRate - 30.0) < 0.1) frameRate = TimecodeUtils::FrameRate::FPS_30;

        return TimecodeUtils::formatTimecode(seconds, TimecodeUtils::Format::FRAMES, frameRate);
    } else {
        // Millisecond-based timecode (HH:MM:SS.mmm)
        return TimecodeUtils::formatTimecode(seconds, TimecodeUtils::Format::MILLISECONDS, TimecodeUtils::FrameRate::FPS_25);
    }
}

void TimelineRuler::drawTimeMarkers(QPainter &painter)
{
    painter.setPen(QColor(180, 180, 180));
    QFont font = painter.font();
    font.setPointSize(9);
    font.setFamily("Monaco, Consolas, monospace");
    painter.setFont(font);

    int width = this->width();

    // Adaptive time step based on zoom level
    double timeStep = 1.0; // 1 second intervals
    if (m_zoom > 4.0) timeStep = 0.1;      // 100ms at high zoom
    else if (m_zoom > 2.0) timeStep = 0.5;  // 500ms at medium zoom
    else if (m_zoom < 0.5) timeStep = 5.0;  // 5s at low zoom
    else if (m_zoom < 0.2) timeStep = 10.0; // 10s at very low zoom

    int labelInterval = (m_zoom > 2.0) ? 10 : 5; // Show fewer labels at high zoom

    for (double time = 0; time <= m_duration; time += timeStep) {
        int x = timeToPixels(time);
        if (x > width) break;

        // Draw tick marks
        int tickHeight = (static_cast<int>(time / timeStep) % labelInterval == 0) ? 15 : 8;
        painter.drawLine(x, m_rulerHeight - tickHeight, x, m_rulerHeight);

        // Draw time labels at major intervals
        if (static_cast<int>(time / timeStep) % labelInterval == 0) {
            QString timeStr = formatTimecode(time);
            QRect textRect = painter.fontMetrics().boundingRect(timeStr);
            painter.drawText(x - textRect.width() / 2, 12, timeStr);
        }
    }
}

void TimelineRuler::drawPlayhead(QPainter &painter)
{
    int playheadX = timeToPixels(m_playheadPosition);
    if (playheadX >= 0 && playheadX <= width()) {
        painter.setPen(QPen(QColor(255, 100, 100), 2));
        painter.drawLine(playheadX, 0, playheadX, height());

        // Draw playhead triangle
        QPolygon triangle;
        triangle << QPoint(playheadX - 5, 0) << QPoint(playheadX + 5, 0) << QPoint(playheadX, 8);
        painter.setBrush(QColor(255, 100, 100));
        painter.drawPolygon(triangle);
    }
}

void TimelineRuler::paintEvent(QPaintEvent *event)
{
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);

    // Background
    painter.fillRect(rect(), QColor(45, 45, 45));

    // Draw time markers
    drawTimeMarkers(painter);

    // Draw playhead
    drawPlayhead(painter);
}

void TimelineRuler::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        double clickTime = pixelsToTime(static_cast<int>(event->position().x()));
        clickTime = qMax(0.0, qMin(clickTime, m_duration));

        if (event->modifiers() & Qt::ShiftModifier) {
            // Shift+click starts time range selection
            if (auto timeline = qobject_cast<TimelineWidget*>(parent()->parent())) {
                timeline->startTimeRangeSelection(clickTime);
                m_dragging = true;
            }
        } else {
            // Normal click moves playhead
            m_playheadPosition = clickTime;
            emit playheadMoved(clickTime);
            update();
        }
    }
}

void TimelineRuler::mouseMoveEvent(QMouseEvent *event)
{
    if (m_dragging && (event->buttons() & Qt::LeftButton)) {
        double currentTime = pixelsToTime(static_cast<int>(event->position().x()));
        currentTime = qMax(0.0, qMin(currentTime, m_duration));

        if (auto timeline = qobject_cast<TimelineWidget*>(parent()->parent())) {
            timeline->updateTimeRangeSelection(currentTime);
        }
    }
}

void TimelineRuler::mouseReleaseEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton && m_dragging) {
        m_dragging = false;

        if (auto timeline = qobject_cast<TimelineWidget*>(parent()->parent())) {
            timeline->finishTimeRangeSelection();
        }
    }
}

void TimelineRuler::wheelEvent(QWheelEvent *event)
{
    // Zoom with mouse wheel
    double factor = event->angleDelta().y() > 0 ? 1.2 : 0.8;
    emit zoomRequested(factor);
    event->accept();
}

void TimelineRuler::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);
    // Ensure ruler updates properly on resize
    update();
}

// TimelineToolbar Implementation
TimelineToolbar::TimelineToolbar(QWidget *parent)
    : QToolBar(parent)
{
    setFixedHeight(TimelineWidget::TOOLBAR_HEIGHT);
    setMovable(false);
    setupActions();
}

void TimelineToolbar::setupActions()
{
    // Zoom controls
    m_zoomInAction = addAction("🔍+", this, &TimelineToolbar::onZoomIn);
    m_zoomInAction->setToolTip("Zoom In");

    m_zoomOutAction = addAction("🔍-", this, &TimelineToolbar::onZoomOut);
    m_zoomOutAction->setToolTip("Zoom Out");

    m_fitToWindowAction = addAction("⤢", this, &TimelineToolbar::onFitToWindow);
    m_fitToWindowAction->setToolTip("Fit to Window");

    addSeparator();

    // Display options
    m_showWaveformsAction = addAction("〰️", this, [this]() {
        qDebug() << "=== WAVEFORM BUTTON CLICKED ===" << "isChecked:" << m_showWaveformsAction->isChecked();
        emit showWaveformsToggled(m_showWaveformsAction->isChecked());
    });
    m_showWaveformsAction->setCheckable(true);
    m_showWaveformsAction->setChecked(false);  // Default to OFF as requested
    m_showWaveformsAction->setToolTip("Show Waveforms");

    m_showRegionNamesAction = addAction("📝", this, [this]() { emit showRegionNamesToggled(m_showRegionNamesAction->isChecked()); });
    m_showRegionNamesAction->setCheckable(true);
    m_showRegionNamesAction->setChecked(true);
    m_showRegionNamesAction->setToolTip("Show Region Names");

    m_toggleRegionNameSourceAction = addAction("🔄", this, &TimelineToolbar::onToggleRegionNameSource);
    m_toggleRegionNameSourceAction->setCheckable(true);
    m_toggleRegionNameSourceAction->setChecked(false);
    m_toggleRegionNameSourceAction->setToolTip("Toggle Region Name Source (A/B)");

    m_showConfidenceAction = addAction("📊", this, [this]() { emit showConfidenceToggled(m_showConfidenceAction->isChecked()); });
    m_showConfidenceAction->setCheckable(true);
    m_showConfidenceAction->setChecked(true);
    m_showConfidenceAction->setToolTip("Show Confidence Scores");

    addSeparator();

    // Track selection
    m_selectAllTracksAction = addAction("☑️", this, &TimelineToolbar::onSelectAllTracks);
    m_selectAllTracksAction->setToolTip("Select All Tracks");

    m_deselectAllTracksAction = addAction("☐", this, &TimelineToolbar::onDeselectAllTracks);
    m_deselectAllTracksAction->setToolTip("Deselect All Tracks");
}

void TimelineToolbar::onZoomIn() { emit zoomInRequested(); }
void TimelineToolbar::onZoomOut() { emit zoomOutRequested(); }
void TimelineToolbar::onFitToWindow() { emit fitToWindowRequested(); }
void TimelineToolbar::onSelectAllTracks() { emit trackSelectionRequested(true); }
void TimelineToolbar::onDeselectAllTracks() { emit trackSelectionRequested(false); }
void TimelineToolbar::onToggleRegionNameSource() {
    // This will be handled by the parent TimelineWidget
    if (auto timeline = qobject_cast<TimelineWidget*>(parent())) {
        timeline->onToggleRegionNameSource();
    }
}

// TransportControls Implementation
TransportControls::TransportControls(QWidget *parent)
    : QWidget(parent)
{
    setFixedHeight(TimelineWidget::TRANSPORT_HEIGHT);
    setupLayout();
}

void TransportControls::setupLayout()
{
    QHBoxLayout *layout = new QHBoxLayout(this);
    layout->setContentsMargins(8, 4, 8, 4);
    layout->setSpacing(8);

    // Transport buttons
    m_playButton = new QToolButton;
    m_playButton->setText("▶");
    m_playButton->setFixedSize(36, 32);
    m_playButton->setToolTip("Play");

    m_pauseButton = new QToolButton;
    m_pauseButton->setText("⏸");
    m_pauseButton->setFixedSize(36, 32);
    m_pauseButton->setToolTip("Pause");

    m_stopButton = new QToolButton;
    m_stopButton->setText("⏹");
    m_stopButton->setFixedSize(36, 32);
    m_stopButton->setToolTip("Stop");

    layout->addWidget(m_playButton);
    layout->addWidget(m_pauseButton);
    layout->addWidget(m_stopButton);

    layout->addWidget(new QFrame); // Spacer

    // Time display
    m_timeLabel = new QLabel("00:00:00:00");
    m_timeLabel->setStyleSheet("QLabel { font-family: 'Monaco', 'Consolas', monospace; font-size: 12px; }");
    m_timeLabel->setMinimumWidth(80);
    layout->addWidget(m_timeLabel);

    // Position slider
    m_positionSlider = new QSlider(Qt::Horizontal);
    m_positionSlider->setRange(0, 1000);
    layout->addWidget(m_positionSlider, 1);

    // Duration display
    m_durationLabel = new QLabel("/ 00:00:00:00");
    m_durationLabel->setStyleSheet("QLabel { font-family: 'Monaco', 'Consolas', monospace; font-size: 12px; }");
    m_durationLabel->setMinimumWidth(100);
    layout->addWidget(m_durationLabel);

    layout->addWidget(new QFrame); // Spacer

    // Frame rate combo
    layout->addWidget(new QLabel("FPS:"));
    m_frameRateCombo = new QComboBox;
    m_frameRateCombo->addItems({"23.976", "24", "25", "29.97", "30"});
    m_frameRateCombo->setCurrentText("25");
    m_frameRateCombo->setFixedWidth(70);
    layout->addWidget(m_frameRateCombo);

    // Timecode format combo
    layout->addWidget(new QLabel("Format:"));
    m_timecodeFormatCombo = new QComboBox;
    m_timecodeFormatCombo->addItems({"HH:MM:SS:FF", "HH:MM:SS.mmm"});
    m_timecodeFormatCombo->setFixedWidth(100);
    layout->addWidget(m_timecodeFormatCombo);

    // Connect signals
    connect(m_playButton, &QToolButton::clicked, this, &TransportControls::onPlayClicked);
    connect(m_pauseButton, &QToolButton::clicked, this, &TransportControls::onPauseClicked);
    connect(m_stopButton, &QToolButton::clicked, this, &TransportControls::onStopClicked);
    connect(m_positionSlider, &QSlider::valueChanged, this, &TransportControls::onPositionSliderChanged);
    connect(m_frameRateCombo, &QComboBox::currentTextChanged, this, &TransportControls::onFrameRateChanged);
    connect(m_timecodeFormatCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &TransportControls::onTimecodeFormatChanged);
}

void TransportControls::setDuration(double duration)
{
    m_duration = duration;
    updateTimeDisplay();
}

void TransportControls::setPosition(double position)
{
    m_position = position;
    updateTimeDisplay();

    if (m_duration > 0) {
        int sliderValue = static_cast<int>((position / m_duration) * 1000);
        m_positionSlider->blockSignals(true);
        m_positionSlider->setValue(sliderValue);
        m_positionSlider->blockSignals(false);
    }
}

void TransportControls::setFrameRate(double frameRate)
{
    m_frameRate = frameRate;
    m_frameRateCombo->blockSignals(true);
    m_frameRateCombo->setCurrentText(QString::number(frameRate));
    m_frameRateCombo->blockSignals(false);
    updateTimeDisplay();
}

void TransportControls::setTimecodeFormat(int format)
{
    m_timecodeFormat = format;
    m_timecodeFormatCombo->blockSignals(true);
    m_timecodeFormatCombo->setCurrentIndex(format);
    m_timecodeFormatCombo->blockSignals(false);
    updateTimeDisplay();
}

void TransportControls::updateTimeDisplay()
{
    m_timeLabel->setText(formatTimecode(m_position));
    m_durationLabel->setText("/ " + formatTimecode(m_duration));
}

QString TransportControls::formatTimecode(double seconds) const
{
    if (m_timecodeFormat == 0) {
        // Frame-based timecode (HH:MM:SS:FF)
        TimecodeUtils::FrameRate frameRate = TimecodeUtils::FrameRate::FPS_25;
        if (qAbs(m_frameRate - 23.976) < 0.1) frameRate = TimecodeUtils::FrameRate::FPS_23_976;
        else if (qAbs(m_frameRate - 24.0) < 0.1) frameRate = TimecodeUtils::FrameRate::FPS_24;
        else if (qAbs(m_frameRate - 25.0) < 0.1) frameRate = TimecodeUtils::FrameRate::FPS_25;
        else if (qAbs(m_frameRate - 29.97) < 0.1) frameRate = TimecodeUtils::FrameRate::FPS_29_97;
        else if (qAbs(m_frameRate - 30.0) < 0.1) frameRate = TimecodeUtils::FrameRate::FPS_30;

        return TimecodeUtils::formatTimecode(seconds, TimecodeUtils::Format::FRAMES, frameRate);
    } else {
        // Millisecond-based timecode (HH:MM:SS.mmm)
        return TimecodeUtils::formatTimecode(seconds, TimecodeUtils::Format::MILLISECONDS, TimecodeUtils::FrameRate::FPS_25);
    }
}

void TransportControls::play()
{
    m_playing = true;
    m_playButton->setStyleSheet("QToolButton { background-color: #4CAF50; }");
    m_pauseButton->setStyleSheet("");
}

void TransportControls::pause()
{
    m_playing = false;
    m_playButton->setStyleSheet("");
    m_pauseButton->setStyleSheet("QToolButton { background-color: #FF9800; }");
}

void TransportControls::stop()
{
    m_playing = false;
    m_position = 0.0;
    setPosition(0.0);
    m_playButton->setStyleSheet("");
    m_pauseButton->setStyleSheet("");
}

void TransportControls::setPlayheadPosition(double position)
{
    setPosition(position);
}

void TransportControls::onPlayClicked()
{
    emit playRequested();
}

void TransportControls::onPauseClicked()
{
    emit pauseRequested();
}

void TransportControls::onStopClicked()
{
    emit stopRequested();
}

void TransportControls::onPositionSliderChanged(int value)
{
    if (m_duration > 0) {
        double newPosition = (value / 1000.0) * m_duration;
        emit positionChanged(newPosition);
    }
}

void TransportControls::onFrameRateChanged()
{
    bool ok;
    double frameRate = m_frameRateCombo->currentText().toDouble(&ok);
    if (ok) {
        m_frameRate = frameRate;
        updateTimeDisplay();
        emit frameRateChanged(frameRate);
    }
}

void TransportControls::onTimecodeFormatChanged()
{
    m_timecodeFormat = m_timecodeFormatCombo->currentIndex();
    updateTimeDisplay();
    emit timecodeFormatChanged(m_timecodeFormat);
}



// TrackHeader Implementation
TrackHeader::TrackHeader(const TimelineTrack &track, QWidget *parent)
    : QWidget(parent), m_track(track)
{
    setFixedWidth(HEADER_WIDTH);
    setMinimumHeight(TimelineWidget::TRACK_HEIGHT);
    setupLayout();
}

void TrackHeader::setupLayout()
{
    QGridLayout *layout = new QGridLayout(this);
    layout->setContentsMargins(8, 4, 8, 4);
    layout->setSpacing(4);

    // Track number label
    m_trackNumberLabel = new QLabel(QString::number(m_track.trackNumber >= 0 ? m_track.trackNumber + 1 : 1));
    m_trackNumberLabel->setStyleSheet("QLabel { color: #888; font-weight: bold; font-size: 10px; }");
    m_trackNumberLabel->setFixedWidth(20);
    layout->addWidget(m_trackNumberLabel, 0, 0);

    // Selection checkbox
    m_selectionCheckBox = new QCheckBox;
    m_selectionCheckBox->setChecked(m_track.selected);
    m_selectionCheckBox->setToolTip("Select track for analysis/export");
    connect(m_selectionCheckBox, &QCheckBox::toggled, this, &TrackHeader::onSelectionClicked);
    layout->addWidget(m_selectionCheckBox, 0, 1);

    // Track name (editable)
    m_nameEdit = new QLineEdit(m_track.name);
    m_nameEdit->setStyleSheet("QLineEdit { background: transparent; border: 1px solid #555; color: white; padding: 2px; }");
    m_nameEdit->setToolTip("Double-click to edit track name");
    connect(m_nameEdit, &QLineEdit::editingFinished, this, &TrackHeader::onNameEditFinished);
    layout->addWidget(m_nameEdit, 0, 2, 1, 4);

    // Control buttons row
    m_muteButton = new QToolButton;
    m_muteButton->setText("M");
    m_muteButton->setFixedSize(BUTTON_SIZE, BUTTON_SIZE);
    m_muteButton->setCheckable(true);
    m_muteButton->setChecked(m_track.muted);
    m_muteButton->setToolTip("Mute track");
    connect(m_muteButton, &QToolButton::clicked, this, &TrackHeader::onMuteClicked);
    layout->addWidget(m_muteButton, 1, 2);

    m_soloButton = new QToolButton;
    m_soloButton->setText("S");
    m_soloButton->setFixedSize(BUTTON_SIZE, BUTTON_SIZE);
    m_soloButton->setCheckable(true);
    m_soloButton->setChecked(m_track.solo);
    m_soloButton->setToolTip("Solo track");
    connect(m_soloButton, &QToolButton::clicked, this, &TrackHeader::onSoloClicked);
    layout->addWidget(m_soloButton, 1, 3);

    m_visibilityButton = new QToolButton;
    m_visibilityButton->setText("👁");
    m_visibilityButton->setFixedSize(BUTTON_SIZE, BUTTON_SIZE);
    m_visibilityButton->setCheckable(true);
    m_visibilityButton->setChecked(m_track.visible);
    m_visibilityButton->setToolTip("Show/hide track");
    connect(m_visibilityButton, &QToolButton::clicked, this, &TrackHeader::onVisibilityClicked);
    layout->addWidget(m_visibilityButton, 1, 4);

    updateButtonStates();
}

void TrackHeader::updateButtonStates()
{
    QString muteStyle = m_track.muted ?
        "QToolButton { background-color: #ff6b6b; color: white; }" :
        "QToolButton { background-color: #555; color: #ccc; }";
    m_muteButton->setStyleSheet(muteStyle);

    QString soloStyle = m_track.solo ?
        "QToolButton { background-color: #ffd93d; color: black; }" :
        "QToolButton { background-color: #555; color: #ccc; }";
    m_soloButton->setStyleSheet(soloStyle);

    QString visStyle = m_track.visible ?
        "QToolButton { background-color: #6bcf7f; color: white; }" :
        "QToolButton { background-color: #555; color: #ccc; }";
    m_visibilityButton->setStyleSheet(visStyle);
}

void TrackHeader::updateTrack(const TimelineTrack &track)
{
    m_track = track;
    m_nameEdit->setText(track.name);
    m_muteButton->setChecked(track.muted);
    m_soloButton->setChecked(track.solo);
    m_visibilityButton->setChecked(track.visible);
    m_selectionCheckBox->setChecked(track.selected);
    m_trackNumberLabel->setText(QString::number(track.trackNumber >= 0 ? track.trackNumber + 1 : 1));
    updateButtonStates();
    update();
}

QString TrackHeader::getTrackName() const
{
    return m_nameEdit->text();
}

bool TrackHeader::isSelected() const
{
    return m_selectionCheckBox->isChecked();
}

void TrackHeader::setSelected(bool selected)
{
    m_selectionCheckBox->setChecked(selected);
}

void TrackHeader::setHeight(int height)
{
    setMinimumHeight(height);
    setMaximumHeight(height);
}

void TrackHeader::paintEvent(QPaintEvent *event)
{
    QPainter painter(this);

    // Background with track color
    QColor bgColor = m_track.color.darker(200);
    painter.fillRect(rect(), bgColor);

    // Border
    painter.setPen(QColor(80, 80, 80));
    painter.drawRect(rect().adjusted(0, 0, -1, -1));

    // Selection highlight
    if (m_track.selected) {
        painter.setPen(QPen(QColor(100, 150, 255), 2));
        painter.drawRect(rect().adjusted(1, 1, -2, -2));
    }
}

void TrackHeader::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        // Allow clicking on the header to toggle selection
        bool newSelection = !m_selectionCheckBox->isChecked();
        m_selectionCheckBox->setChecked(newSelection);
        emit selectionToggled(m_track.name, newSelection);
    }
    QWidget::mousePressEvent(event);
}

void TrackHeader::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);
    // Ensure header redraws properly on resize
    update();
}

void TrackHeader::onMuteClicked()
{
    m_track.muted = m_muteButton->isChecked();
    updateButtonStates();
    emit muteToggled(m_track.name, m_track.muted);
}

void TrackHeader::onSoloClicked()
{
    m_track.solo = m_soloButton->isChecked();
    updateButtonStates();
    emit soloToggled(m_track.name, m_track.solo);
}

void TrackHeader::onVisibilityClicked()
{
    m_track.visible = m_visibilityButton->isChecked();
    updateButtonStates();
    emit visibilityToggled(m_track.name, m_track.visible);
}

void TrackHeader::onSelectionClicked()
{
    m_track.selected = m_selectionCheckBox->isChecked();
    update(); // Refresh selection highlight
    emit selectionToggled(m_track.name, m_track.selected);
}

void TrackHeader::onNameEditFinished()
{
    QString newName = m_nameEdit->text().trimmed();
    if (!newName.isEmpty() && newName != m_track.name) {
        QString oldName = m_track.name;
        m_track.name = newName;
        emit trackRenamed(oldName, newName);
    }
}

// TrackContent Implementation
TrackContent::TrackContent(const TimelineTrack &track, QWidget *parent)
    : QWidget(parent), m_track(track)
{
    setFixedHeight(TimelineWidget::TRACK_HEIGHT);
    setMinimumWidth(1000);
    setMouseTracking(true);

    // Optimize rendering and prevent visual artifacts
    setAttribute(Qt::WA_OpaquePaintEvent, true);
    setAutoFillBackground(false); // Let paintEvent handle background
    setContentsMargins(0, 0, 0, 0);
    setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);

    // Prevent scrolling artifacts and improve performance
    setAttribute(Qt::WA_NoSystemBackground, true);
    setAttribute(Qt::WA_PaintOnScreen, false);
    setAttribute(Qt::WA_StaticContents, true); // Content doesn't change during scrolling

    // Set background color
    QPalette trackPalette = palette();
    trackPalette.setColor(QPalette::Window, QColor(35, 35, 35));
    setPalette(trackPalette);
}

void TrackContent::setTrack(const TimelineTrack &track)
{
    m_track = track;
    update();
}

void TrackContent::setZoom(double zoom)
{
    m_zoom = qMax(0.1, qMin(10.0, zoom)); // Clamp zoom
    int newWidth = timeToPixels(m_duration);
    setMinimumWidth(newWidth);
    // Use setFixedWidth for proper scroll area handling
    setFixedWidth(newWidth);

    // Ensure widget respects parent boundaries
    if (parentWidget()) {
        setMaximumWidth(qMax(newWidth, parentWidget()->width()));
    }

    update();
}

void TrackContent::setDuration(double duration)
{
    m_duration = duration;
    int newWidth = timeToPixels(duration);
    setMinimumWidth(newWidth);
    setFixedWidth(newWidth);
    update();
}

void TrackContent::setPlayheadPosition(double position)
{
    m_playheadPosition = position;
    update();
}

void TrackContent::setTimeRangeHighlight(double startTime, double endTime, bool enabled)
{
    m_timeRangeHighlightEnabled = enabled;
    m_timeRangeStartTime = startTime;
    m_timeRangeEndTime = endTime;
    update();
}

void TrackContent::setShowWaveforms(bool show)
{
    qDebug() << "TrackContent::setShowWaveforms called with:" << show << "previous state:" << m_showWaveforms;
    m_showWaveforms = show;
    qDebug() << "TrackContent calling update() to refresh display";
    update();
}

void TrackContent::setShowRegionNames(bool show)
{
    m_showRegionNames = show;
    update();
}

void TrackContent::setShowConfidence(bool show)
{
    m_showConfidence = show;
    update();
}

void TrackContent::setTimelineWidget(TimelineWidget *timelineWidget)
{
    m_timelineWidget = timelineWidget;
}

double TrackContent::pixelsToTime(int pixels) const
{
    return (pixels / m_zoom) / 100.0;
}

int TrackContent::timeToPixels(double time) const
{
    return static_cast<int>(time * 100.0 * m_zoom);
}

void TrackContent::paintEvent(QPaintEvent *event)
{
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing, true);

    // Enable composition mode for better rendering during scrolling
    painter.setCompositionMode(QPainter::CompositionMode_SourceOver);

    QRect exposedRect = event->rect();
    QRect widgetRect = rect();

    // Ensure we only paint within widget bounds with extra safety margin
    QRect clippedRect = exposedRect.intersected(widgetRect);
    if (clippedRect.isEmpty()) {
        return;
    }

    // Set strict clipping to prevent overflow and artifacts
    painter.setClipRect(clippedRect, Qt::IntersectClip);

    // Save painter state for restoration
    painter.save();

    // Background with proper bounds checking
    painter.fillRect(clippedRect, QColor(35, 35, 35));

    // Track border (only draw if visible)
    painter.setPen(QColor(60, 60, 60));
    if (widgetRect.intersects(clippedRect)) {
        painter.drawRect(widgetRect.adjusted(0, 0, -1, -1));
    }

    // Draw time range highlight if enabled
    if (m_timeRangeHighlightEnabled) {
        drawTimeRangeHighlight(painter);
    }

    // Draw regions with optimized visibility checking
    for (const auto &region : m_track.regions) {
        int regionStartX = timeToPixels(region.startTime);
        int regionWidth = qMax(1, timeToPixels(region.duration));

        // Skip regions completely outside visible area
        if (regionStartX + regionWidth < clippedRect.left() || regionStartX > clippedRect.right()) {
            continue;
        }

        // Only draw regions that intersect with the clipped area
        QRect regionRect(regionStartX, 4, regionWidth, height() - 8);
        if (regionRect.intersects(clippedRect)) {
            drawRegion(painter, region);
        }
    }

    // Draw playhead
    drawPlayhead(painter);

    // Restore painter state
    painter.restore();
}

void TrackContent::drawTimeRangeHighlight(QPainter &painter)
{
    if (!m_timeRangeHighlightEnabled) return;

    int startX = timeToPixels(m_timeRangeStartTime);
    int endX = timeToPixels(m_timeRangeEndTime);

    QRect highlightRect(startX, 0, endX - startX, height());
    painter.fillRect(highlightRect, QColor(100, 150, 255, 40));

    // Draw borders
    painter.setPen(QPen(QColor(100, 150, 255), 2));
    painter.drawLine(startX, 0, startX, height());
    painter.drawLine(endX, 0, endX, height());
}

void TrackContent::drawPlayhead(QPainter &painter)
{
    int playheadX = timeToPixels(m_playheadPosition);
    if (playheadX >= 0 && playheadX <= width()) {
        painter.setPen(QPen(QColor(255, 100, 100), 2));
        painter.drawLine(playheadX, 0, playheadX, height());
    }
}

void TrackContent::drawWaveform(QPainter &painter, const TimelineRegion &region, const QRect &rect)
{
    qDebug() << "drawWaveform called for region:" << region.id << "m_showWaveforms:" << m_showWaveforms << "rect.width():" << rect.width();
    if (!m_showWaveforms || rect.width() < 20) {
        qDebug() << "drawWaveform early return - showWaveforms:" << m_showWaveforms << "width:" << rect.width();
        return;
    }

    // Try to get real waveform data from shared cache
    WaveformData *waveformData = SharedWaveformCache::instance().getWaveformData(region.id);
    qDebug() << "Waveform data for region" << region.id << ":"
             << "exists:" << (waveformData != nullptr)
             << "complete:" << (waveformData ? waveformData->isComplete : false)
             << "calculating:" << (waveformData ? waveformData->isCalculating : false)
             << "peaks size:" << (waveformData ? waveformData->peaks.size() : 0)
             << "shared cache size:" << SharedWaveformCache::instance().size();

    painter.setPen(QColor(150, 150, 150, 100));
    int centerY = rect.center().y();
    int maxAmplitude = rect.height() / 4; // Maximum waveform amplitude

    if (waveformData && waveformData->isComplete && !waveformData->peaks.isEmpty()) {
        qDebug() << "Drawing REAL waveform data for region" << region.id << "with" << waveformData->peaks.size() << "samples";
        // Draw real waveform data
        int numSamples = waveformData->peaks.size();
        double samplesPerPixel = double(numSamples) / rect.width();

        for (int x = rect.left(); x < rect.right(); x += 1) {
            int sampleIndex = int((x - rect.left()) * samplesPerPixel);
            if (sampleIndex >= 0 && sampleIndex < numSamples) {
                float peakValue = waveformData->peaks[sampleIndex];
                int amplitude = int(peakValue * maxAmplitude);

                // Draw peak waveform
                painter.setPen(QColor(120, 180, 255, 150));
                painter.drawLine(x, centerY - amplitude, x, centerY + amplitude);

                // Draw RMS waveform (if available)
                if (sampleIndex < waveformData->rms.size()) {
                    float rmsValue = waveformData->rms[sampleIndex];
                    int rmsAmplitude = int(rmsValue * maxAmplitude);
                    painter.setPen(QColor(80, 140, 200, 100));
                    painter.drawLine(x, centerY - rmsAmplitude, x, centerY + rmsAmplitude);
                }
            }
        }
    } else {
        // Show placeholder indicating waveform is being calculated or unavailable
        if (waveformData && waveformData->isCalculating) {
            qDebug() << "Drawing CALCULATING indicator for region" << region.id;
            // Show "calculating" indicator
            painter.setPen(QColor(255, 200, 100, 150));
            painter.drawText(rect, Qt::AlignCenter, "Calculating...");
        } else {
            qDebug() << "Drawing PLACEHOLDER pattern for region" << region.id;
            // Show simple placeholder pattern - make it more visible for debugging
            painter.setPen(QColor(255, 100, 100, 200)); // Bright red for debugging
            for (int x = rect.left(); x < rect.right(); x += 4) {
                int amplitude = 10; // Larger placeholder amplitude for visibility
                painter.drawLine(x, centerY - amplitude, x, centerY + amplitude);
            }
        }
    }
}

void TrackContent::drawRegion(QPainter &painter, const TimelineRegion &region)
{
    int x = timeToPixels(region.startTime);
    int width = qMax(1, timeToPixels(region.duration));
    int y = 4;
    int height = this->height() - 8;

    QRect regionRect(x, y, width, height);

    // Region background with gradient
    QColor bgColor = region.color;
    if (region.selected) {
        bgColor = bgColor.lighter(140);
    }
    if (region.muted) {
        bgColor = bgColor.darker(180);
    }

    // Create gradient for depth
    QLinearGradient gradient(regionRect.topLeft(), regionRect.bottomLeft());
    gradient.setColorAt(0, bgColor.lighter(110));
    gradient.setColorAt(1, bgColor.darker(110));
    painter.fillRect(regionRect, gradient);

    // Draw waveform if enabled and region is wide enough
    if (m_showWaveforms && regionRect.width() > 30) {
        drawWaveform(painter, region, regionRect);
    }

    // Region border
    QPen borderPen;
    if (region.selected) {
        borderPen = QPen(QColor(100, 150, 255), 2);
    } else {
        borderPen = QPen(QColor(120, 120, 120), 1);
    }
    painter.setPen(borderPen);
    painter.drawRect(regionRect);

    // Region text (only if enabled and region is wide enough)
    if (m_showRegionNames && regionRect.width() > 50) {
        painter.setPen(QColor(255, 255, 255));
        QFont font = painter.font();
        font.setPointSize(9);
        font.setBold(region.selected);
        painter.setFont(font);

        // Choose region name based on toggle state
        QString text;
        if (m_timelineWidget && m_timelineWidget->useAlternativeRegionNames() && !region.alternativeName.isEmpty()) {
            text = region.alternativeName;
        } else {
            text = region.name;
        }

        if (!region.speakerId.isEmpty() && regionRect.width() > 100) {
            text += QString(" (%1)").arg(region.speakerId);
        }

        QRect textRect = regionRect.adjusted(4, 2, -4, -2);
        painter.drawText(textRect, Qt::AlignLeft | Qt::AlignTop | Qt::TextWordWrap, text);
    }

    // Confidence indicator (only if enabled)
    if (m_showConfidence && region.confidence > 0 && regionRect.width() > 40) {
        QString confText = QString("%1%").arg(static_cast<int>(region.confidence * 100));
        QColor confColor = region.confidence > 0.8 ? QColor(100, 255, 100) :
                          region.confidence > 0.5 ? QColor(255, 255, 100) : QColor(255, 150, 150);
        painter.setPen(confColor);
        QFont confFont = painter.font();
        confFont.setPointSize(8);
        painter.setFont(confFont);
        painter.drawText(regionRect.adjusted(2, 2, -2, -2), Qt::AlignRight | Qt::AlignBottom, confText);
    }

    // Content type indicator
    if (regionRect.width() > 20) {
        QString typeIndicator;
        if (region.contentType == "Dialogue") typeIndicator = "D";
        else if (region.contentType == "Music") typeIndicator = "M";
        else if (region.contentType == "SFX") typeIndicator = "S";
        else if (region.contentType == "Ambience") typeIndicator = "A";

        if (!typeIndicator.isEmpty()) {
            painter.setPen(QColor(200, 200, 200));
            QFont typeFont = painter.font();
            typeFont.setPointSize(7);
            typeFont.setBold(true);
            painter.setFont(typeFont);
            painter.drawText(regionRect.adjusted(2, 2, -2, -2), Qt::AlignLeft | Qt::AlignBottom, typeIndicator);
        }
    }
}

void TrackContent::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        TimelineRegion *region = getRegionAt(event->position().toPoint());
        if (region) {
            emit regionSelected(region->id);
            m_draggedRegion = region;
            m_dragging = true;
            m_dragStartPos = event->position().toPoint();
            m_dragStartTime = region->startTime;
        }
    }
}

void TrackContent::mouseMoveEvent(QMouseEvent *event)
{
    if (m_dragging && m_draggedRegion) {
        int deltaX = static_cast<int>(event->position().x()) - m_dragStartPos.x();
        double deltaTime = pixelsToTime(deltaX);
        double newStartTime = qMax(0.0, m_dragStartTime + deltaTime);

        m_draggedRegion->startTime = newStartTime;
        update();
    }
}

void TrackContent::mouseReleaseEvent(QMouseEvent *event)
{
    if (m_dragging && m_draggedRegion) {
        emit regionMoved(m_draggedRegion->id, m_draggedRegion->startTime);
        m_dragging = false;
        m_draggedRegion = nullptr;
    }
}

void TrackContent::contextMenuEvent(QContextMenuEvent *event)
{
    TimelineRegion *region = getRegionAt(event->pos());
    if (region) {
        emit regionContextMenu(region->id, event->globalPos());
    }
}

void TrackContent::wheelEvent(QWheelEvent *event)
{
    // Forward zoom requests to timeline
    double factor = event->angleDelta().y() > 0 ? 1.2 : 0.8;
    emit zoomRequested(factor);
    event->accept();
}

void TrackContent::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);
    // Ensure content redraws properly on resize
    update();
}

TimelineRegion* TrackContent::getRegionAt(const QPoint &pos)
{
    for (auto &region : m_track.regions) {
        int x = timeToPixels(region.startTime);
        int width = timeToPixels(region.duration);
        QRect regionRect(x, 5, width, height() - 10);

        if (regionRect.contains(pos)) {
            return &region;
        }
    }
    return nullptr;
}

// RegionMetadataPanel Implementation
RegionMetadataPanel::RegionMetadataPanel(QWidget *parent)
    : QWidget(parent), m_compactMode(false)
{
    setFixedWidth(280);
    setupLayout();
}

void RegionMetadataPanel::setupLayout()
{
    QVBoxLayout *layout = new QVBoxLayout(this);
    layout->setContentsMargins(8, 8, 8, 8);
    layout->setSpacing(6);

    // Basic info (always visible)
    m_basicInfoWidget = new QWidget;
    QFormLayout *basicLayout = new QFormLayout(m_basicInfoWidget);
    basicLayout->setContentsMargins(0, 0, 0, 0);

    m_nameEdit = new QLineEdit;
    m_nameEdit->setPlaceholderText("Region name");

    m_speakerEdit = new QLineEdit;
    m_speakerEdit->setPlaceholderText("Speaker ID");

    m_contentTypeCombo = new QComboBox;
    m_contentTypeCombo->addItems({"Dialogue", "Music", "SFX", "Ambience", "Silence", "Unknown"});

    basicLayout->addRow("Name:", m_nameEdit);
    basicLayout->addRow("Speaker:", m_speakerEdit);
    basicLayout->addRow("Type:", m_contentTypeCombo);

    layout->addWidget(m_basicInfoWidget);

    // Advanced info (collapsible in compact mode)
    m_advancedInfoWidget = new QWidget;
    QVBoxLayout *advancedLayout = new QVBoxLayout(m_advancedInfoWidget);
    advancedLayout->setContentsMargins(0, 0, 0, 0);

    // Timing group
    QGroupBox *timingGroup = new QGroupBox("Timing");
    QFormLayout *timingLayout = new QFormLayout(timingGroup);

    m_startTimeSpin = new QDoubleSpinBox;
    m_startTimeSpin->setRange(0.0, 99999.0);
    m_startTimeSpin->setDecimals(3);
    m_startTimeSpin->setSuffix(" s");

    m_durationSpin = new QDoubleSpinBox;
    m_durationSpin->setRange(0.001, 99999.0);
    m_durationSpin->setDecimals(3);
    m_durationSpin->setSuffix(" s");

    timingLayout->addRow("Start:", m_startTimeSpin);
    timingLayout->addRow("Duration:", m_durationSpin);

    // Analysis group
    QGroupBox *analysisGroup = new QGroupBox("Analysis");
    QFormLayout *analysisLayout = new QFormLayout(analysisGroup);

    m_confidenceSpin = new QDoubleSpinBox;
    m_confidenceSpin->setRange(0.0, 1.0);
    m_confidenceSpin->setDecimals(2);
    m_confidenceSpin->setSuffix("%");
    m_confidenceSpin->setReadOnly(true);

    m_mutedCheck = new QCheckBox;

    analysisLayout->addRow("Confidence:", m_confidenceSpin);
    analysisLayout->addRow("Muted:", m_mutedCheck);

    // Notes
    QGroupBox *notesGroup = new QGroupBox("Notes");
    QVBoxLayout *notesLayout = new QVBoxLayout(notesGroup);

    m_notesEdit = new QTextEdit;
    m_notesEdit->setMaximumHeight(80);
    m_notesEdit->setPlaceholderText("Additional notes...");
    notesLayout->addWidget(m_notesEdit);

    advancedLayout->addWidget(timingGroup);
    advancedLayout->addWidget(analysisGroup);
    advancedLayout->addWidget(notesGroup);

    layout->addWidget(m_advancedInfoWidget);
    layout->addStretch();

    // Connect signals
    connect(m_nameEdit, &QLineEdit::textChanged, this, &RegionMetadataPanel::onMetadataChanged);
    connect(m_speakerEdit, &QLineEdit::textChanged, this, &RegionMetadataPanel::onMetadataChanged);
    connect(m_contentTypeCombo, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &RegionMetadataPanel::onMetadataChanged);
    connect(m_startTimeSpin, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &RegionMetadataPanel::onMetadataChanged);
    connect(m_durationSpin, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &RegionMetadataPanel::onMetadataChanged);
    connect(m_mutedCheck, &QCheckBox::toggled, this, &RegionMetadataPanel::onMetadataChanged);
    connect(m_notesEdit, &QTextEdit::textChanged, this, &RegionMetadataPanel::onMetadataChanged);

    updateLayout();
}

void RegionMetadataPanel::setCompactMode(bool compact)
{
    m_compactMode = compact;
    updateLayout();
}

void RegionMetadataPanel::updateLayout()
{
    if (m_compactMode) {
        setFixedWidth(250);
        m_advancedInfoWidget->setVisible(false);
    } else {
        setFixedWidth(280);
        m_advancedInfoWidget->setVisible(true);
    }
}

void RegionMetadataPanel::setRegion(const TimelineRegion &region)
{
    m_currentRegionId = region.id;

    m_nameEdit->setText(region.name);
    m_speakerEdit->setText(region.speakerId);
    m_contentTypeCombo->setCurrentText(region.contentType);
    m_startTimeSpin->setValue(region.startTime);
    m_durationSpin->setValue(region.duration);
    m_confidenceSpin->setValue(region.confidence * 100);
    m_mutedCheck->setChecked(region.muted);
    m_notesEdit->setPlainText(region.metadata.value("notes").toString());
}

void RegionMetadataPanel::clearRegion()
{
    m_currentRegionId.clear();
    m_nameEdit->clear();
    m_speakerEdit->clear();
    m_contentTypeCombo->setCurrentIndex(0);
    m_startTimeSpin->setValue(0.0);
    m_durationSpin->setValue(0.0);
    m_confidenceSpin->setValue(0.0);
    m_mutedCheck->setChecked(false);
    m_notesEdit->clear();
}

void RegionMetadataPanel::onMetadataChanged()
{
    if (m_currentRegionId.isEmpty()) return;

    QVariantMap metadata;
    metadata["name"] = m_nameEdit->text();
    metadata["speakerId"] = m_speakerEdit->text();
    metadata["contentType"] = m_contentTypeCombo->currentText();
    metadata["startTime"] = m_startTimeSpin->value();
    metadata["duration"] = m_durationSpin->value();
    metadata["muted"] = m_mutedCheck->isChecked();
    metadata["notes"] = m_notesEdit->toPlainText();

    emit regionUpdated(m_currentRegionId, metadata);
}

// TimelineWidget Implementation
TimelineWidget::TimelineWidget(QWidget *parent)
    : QWidget(parent)
{
    qDebug() << "=== TimelineWidget CONSTRUCTOR CALLED ===";
    qDebug() << "TimelineWidget address:" << this;
    setupUI();
    setupLayout();
    setupConnections();
}

void TimelineWidget::setupUI()
{
    // Create main components
    m_toolbar = new TimelineToolbar(this);
    m_transportControls = new TransportControls(this);
    m_ruler = new TimelineRuler(this);
    m_metadataPanel = new RegionMetadataPanel(this);
    m_metadataPanel->setCompactMode(true);

    // Analysis Range Controls
    m_analysisRangeGroup = new QGroupBox("Analysis Time Range");

    m_analyzeFullAAFCheckBox = new QCheckBox("Analyze Full AAF File");
    m_analyzeFullAAFCheckBox->setChecked(true);

    m_startTimeEdit = new QLineEdit;
    m_startTimeEdit->setPlaceholderText(TimecodeUtils::getPlaceholderText(TimecodeUtils::Format::FRAMES));
    m_startTimeEdit->setText("00:00:10:00"); // Set default start time
    m_startTimeEdit->setEnabled(false);
    m_startTimeEdit->setToolTip("Start time in SMPTE format\nSupported formats: HH:MM:SS:FF or HH:MM:SS.mmm");
    m_startTimeEdit->setInputMask("99:99:99:99"); // Add input mask for SMPTE timecode
    m_startTimeEdit->setMaxLength(11); // Limit length to prevent corruption

    m_endTimeEdit = new QLineEdit;
    m_endTimeEdit->setPlaceholderText(TimecodeUtils::getPlaceholderText(TimecodeUtils::Format::FRAMES));
    m_endTimeEdit->setText("00:01:00:00"); // Set default end time
    m_endTimeEdit->setEnabled(false);
    m_endTimeEdit->setToolTip("End time in SMPTE format\nSupported formats: HH:MM:SS:FF or HH:MM:SS.mmm");
    m_endTimeEdit->setInputMask("99:99:99:99"); // Add input mask for SMPTE timecode
    m_endTimeEdit->setMaxLength(11); // Limit length to prevent corruption

    m_setFromTimelineButton = new QPushButton("Set from Timeline");
    m_setFromTimelineButton->setEnabled(false);
    m_setFromTimelineButton->setToolTip("Use current timeline selection as analysis range");

    m_clearTimeRangeButton = new QPushButton("Clear");
    m_clearTimeRangeButton->setEnabled(false);
    m_clearTimeRangeButton->setToolTip("Clear time range selection");

    // Settings
    m_preserveTimingCheckBox = new QCheckBox("Preserve Original Timing");
    m_preserveTimingCheckBox->setChecked(true);
    m_preserveTimingCheckBox->setToolTip("Prevents regions from being moved in time when exported to DAW");
}

void TimelineWidget::setupLayout()
{
    setMinimumSize(1000, 600);
    setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);

    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(4, 4, 4, 4);
    m_mainLayout->setSpacing(2);

    // Top toolbar
    m_mainLayout->addWidget(m_toolbar);

    // Transport controls with settings
    QWidget *transportWidget = new QWidget;
    QHBoxLayout *transportLayout = new QHBoxLayout(transportWidget);
    transportLayout->setContentsMargins(0, 0, 0, 0);
    transportLayout->addWidget(m_transportControls, 1);
    transportLayout->addWidget(m_preserveTimingCheckBox);
    m_mainLayout->addWidget(transportWidget);

    // Main timeline splitter
    m_timelineSplitter = new QSplitter(Qt::Horizontal);

    // Timeline area
    QWidget *timelineArea = new QWidget;
    QVBoxLayout *timelineLayout = new QVBoxLayout(timelineArea);
    timelineLayout->setContentsMargins(0, 0, 0, 0);
    timelineLayout->setSpacing(0);

    // Ruler container with fixed left margin for track headers
    QWidget *rulerContainer = new QWidget;
    rulerContainer->setFixedHeight(30);
    rulerContainer->setContentsMargins(0, 0, 0, 0);
    QHBoxLayout *rulerLayout = new QHBoxLayout(rulerContainer);
    rulerLayout->setContentsMargins(0, 0, 0, 0);
    rulerLayout->setSpacing(0);

    // Fixed spacer for track header width (150px)
    QWidget *rulerSpacer = new QWidget;
    rulerSpacer->setFixedWidth(150);
    rulerSpacer->setStyleSheet("background-color: #2a2a2a; border-right: 1px solid #555;");
    rulerLayout->addWidget(rulerSpacer);

    // Ruler in scrollable area
    rulerLayout->addWidget(m_ruler, 1);
    timelineLayout->addWidget(rulerContainer);

    // Main timeline content area with fixed headers + scrollable content
    QWidget *timelineContentWidget = new QWidget;
    timelineContentWidget->setContentsMargins(0, 0, 0, 0);
    timelineContentWidget->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);

    QHBoxLayout *timelineContentLayout = new QHBoxLayout(timelineContentWidget);
    timelineContentLayout->setContentsMargins(0, 0, 0, 0);
    timelineContentLayout->setSpacing(0);

    // Fixed track headers area with internal scroll synchronization
    m_trackHeadersArea = new QWidget;
    m_trackHeadersArea->setFixedWidth(150);
    m_trackHeadersArea->setContentsMargins(0, 0, 0, 0);
    m_trackHeadersArea->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Expanding);
    m_trackHeadersArea->setStyleSheet("background-color: #2a2a2a; border-right: 1px solid #555;");

    // Create a scroll area for headers to synchronize with content
    m_trackHeadersScrollArea = new QScrollArea;
    m_trackHeadersScrollArea->setFixedWidth(150);
    m_trackHeadersScrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarNever);
    m_trackHeadersScrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarNever); // Hide scrollbar, sync with main area
    m_trackHeadersScrollArea->setFrameStyle(QFrame::NoFrame);
    m_trackHeadersScrollArea->setWidgetResizable(false);
    m_trackHeadersScrollArea->setStyleSheet("background-color: #2a2a2a; border-right: 1px solid #555;");

    // Container for header widgets
    m_trackHeadersContainer = new QWidget;
    m_trackHeadersContainer->setContentsMargins(0, 0, 0, 0);
    m_trackHeadersContainer->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Fixed);

    m_trackHeadersLayout = new QVBoxLayout(m_trackHeadersContainer);
    m_trackHeadersLayout->setContentsMargins(0, 0, 0, 0);
    m_trackHeadersLayout->setSpacing(0);

    m_trackHeadersScrollArea->setWidget(m_trackHeadersContainer);

    // Add scroll area to the fixed area
    QVBoxLayout *headerAreaLayout = new QVBoxLayout(m_trackHeadersArea);
    headerAreaLayout->setContentsMargins(0, 0, 0, 0);
    headerAreaLayout->setSpacing(0);
    headerAreaLayout->addWidget(m_trackHeadersScrollArea);

    timelineContentLayout->addWidget(m_trackHeadersArea);

    // Scrollable timeline content with STRICT clipping and containment
    m_timelineScrollArea = new QScrollArea;
    m_timelineScrollArea->setWidgetResizable(false);
    m_timelineScrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_timelineScrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_timelineScrollArea->setFrameStyle(QFrame::NoFrame);

    // CRITICAL: Enable strict clipping to prevent overflow
    m_timelineScrollArea->setStyleSheet(
        "QScrollArea { "
        "   border: none; "
        "   background-color: #1e1e1e; "
        "   overflow: hidden; "
        "} "
        "QScrollArea > QWidget > QWidget { "
        "   background-color: #1e1e1e; "
        "}"
    );

    // Configure viewport with strict clipping
    if (m_timelineScrollArea->viewport()) {
        m_timelineScrollArea->viewport()->setAutoFillBackground(true);
        QPalette viewportPalette = m_timelineScrollArea->viewport()->palette();
        viewportPalette.setColor(QPalette::Window, QColor(30, 30, 30));
        m_timelineScrollArea->viewport()->setPalette(viewportPalette);
    }

    // Create timeline container with strict size constraints
    m_timelineContainer = new QWidget;
    m_timelineContainer->setContentsMargins(0, 0, 0, 0);
    m_timelineContainer->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Fixed);

    m_timelineLayout = new QVBoxLayout(m_timelineContainer);
    m_timelineLayout->setContentsMargins(0, 0, 0, 0);
    m_timelineLayout->setSpacing(0);
    m_timelineLayout->setSizeConstraint(QLayout::SetFixedSize);

    m_timelineScrollArea->setWidget(m_timelineContainer);
    timelineContentLayout->addWidget(m_timelineScrollArea, 1);

    timelineLayout->addWidget(timelineContentWidget, 1);

    // Create right panel with metadata and analysis range controls - FIXED POSITION
    QWidget *rightPanel = new QWidget;
    rightPanel->setFixedWidth(280);
    rightPanel->setContentsMargins(0, 0, 0, 0);
    rightPanel->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Expanding);
    // Prevent overflow with stylesheet instead of attribute

    QVBoxLayout *rightPanelLayout = new QVBoxLayout(rightPanel);
    rightPanelLayout->setContentsMargins(4, 4, 4, 4);
    rightPanelLayout->setSpacing(8);
    rightPanelLayout->setSizeConstraint(QLayout::SetMinAndMaxSize); // Prevent layout overflow

    // Setup analysis range group layout - FIXED AT TOP
    m_analysisRangeGroup->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed);
    QVBoxLayout *analysisRangeLayout = new QVBoxLayout(m_analysisRangeGroup);
    analysisRangeLayout->setContentsMargins(8, 8, 8, 8);
    analysisRangeLayout->setSpacing(6);
    analysisRangeLayout->addWidget(m_analyzeFullAAFCheckBox);

    // Time range input controls
    QWidget *timeInputWidget = new QWidget;
    timeInputWidget->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed);
    QFormLayout *timeInputLayout = new QFormLayout(timeInputWidget);
    timeInputLayout->setContentsMargins(0, 0, 0, 0);
    timeInputLayout->addRow("Start Time:", m_startTimeEdit);
    timeInputLayout->addRow("End Time:", m_endTimeEdit);
    analysisRangeLayout->addWidget(timeInputWidget);

    // Time range buttons
    QWidget *timeButtonWidget = new QWidget;
    timeButtonWidget->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed);
    QHBoxLayout *timeButtonLayout = new QHBoxLayout(timeButtonWidget);
    timeButtonLayout->setContentsMargins(0, 0, 0, 0);
    timeButtonLayout->addWidget(m_setFromTimelineButton);
    timeButtonLayout->addWidget(m_clearTimeRangeButton);
    timeButtonLayout->addStretch();
    analysisRangeLayout->addWidget(timeButtonWidget);

    // Add components to right panel with proper constraints
    rightPanelLayout->addWidget(m_analysisRangeGroup, 0); // No stretch - keep at top
    rightPanelLayout->addWidget(m_metadataPanel, 1); // Give metadata panel stretch factor

    // Add timeline area and right panel to splitter
    m_timelineSplitter->addWidget(timelineArea);
    m_timelineSplitter->addWidget(rightPanel);
    m_timelineSplitter->setSizes({800, 280});

    m_mainLayout->addWidget(m_timelineSplitter, 1);
}

void TimelineWidget::setupConnections()
{
    // Ruler connections
    connect(m_ruler, &TimelineRuler::playheadMoved, this, &TimelineWidget::onPlayheadMoved);
    connect(m_ruler, &TimelineRuler::zoomRequested, this, &TimelineWidget::onZoomRequested);

    // Transport controls connections
    connect(m_transportControls, &TransportControls::playRequested, this, &TimelineWidget::onTransportPlayRequested);
    connect(m_transportControls, &TransportControls::pauseRequested, this, &TimelineWidget::onTransportPauseRequested);
    connect(m_transportControls, &TransportControls::stopRequested, this, &TimelineWidget::onTransportStopRequested);
    connect(m_transportControls, &TransportControls::positionChanged, this, &TimelineWidget::onTransportPositionChanged);
    connect(m_transportControls, &TransportControls::frameRateChanged, this, &TimelineWidget::onFrameRateChanged);
    connect(m_transportControls, &TransportControls::timecodeFormatChanged, this, &TimelineWidget::onTimecodeFormatChanged);

    // Toolbar connections
    qDebug() << "=== SETTING UP TOOLBAR CONNECTIONS ===";
    qDebug() << "m_toolbar address:" << m_toolbar;
    connect(m_toolbar, &TimelineToolbar::zoomInRequested, this, &TimelineWidget::onToolbarZoomIn);
    connect(m_toolbar, &TimelineToolbar::zoomOutRequested, this, &TimelineWidget::onToolbarZoomOut);
    connect(m_toolbar, &TimelineToolbar::fitToWindowRequested, this, &TimelineWidget::onToolbarFitToWindow);
    qDebug() << "Connecting showWaveformsToggled signal from toolbar to TimelineWidget::onShowWaveformsToggled";
    connect(m_toolbar, &TimelineToolbar::showWaveformsToggled, this, &TimelineWidget::onShowWaveformsToggled);
    connect(m_toolbar, &TimelineToolbar::showRegionNamesToggled, this, &TimelineWidget::onShowRegionNamesToggled);
    connect(m_toolbar, &TimelineToolbar::showConfidenceToggled, this, &TimelineWidget::onShowConfidenceToggled);
    connect(m_toolbar, &TimelineToolbar::trackSelectionRequested, this, &TimelineWidget::onToolbarTrackSelection);

    // Metadata panel connections
    connect(m_metadataPanel, &RegionMetadataPanel::regionUpdated, this, &TimelineWidget::regionUpdated);

    // Analysis range control connections (use queued connections to prevent crashes during initialization)
    connect(m_analyzeFullAAFCheckBox, &QCheckBox::toggled, this, &TimelineWidget::onAnalysisRangeChanged, Qt::QueuedConnection);
    connect(m_startTimeEdit, &QLineEdit::textChanged, this, &TimelineWidget::onAnalysisRangeChanged, Qt::QueuedConnection);
    connect(m_endTimeEdit, &QLineEdit::textChanged, this, &TimelineWidget::onAnalysisRangeChanged, Qt::QueuedConnection);
    connect(m_setFromTimelineButton, &QPushButton::clicked, this, &TimelineWidget::onSetTimeRangeFromTimeline, Qt::QueuedConnection);
    connect(m_clearTimeRangeButton, &QPushButton::clicked, this, &TimelineWidget::onClearTimeRange, Qt::QueuedConnection);

    // Scroll synchronization - connect after scroll area is created
    QTimer::singleShot(0, this, [this]() {
        if (m_timelineScrollArea) {
            if (m_timelineScrollArea->horizontalScrollBar()) {
                connect(m_timelineScrollArea->horizontalScrollBar(), &QScrollBar::valueChanged,
                        this, &TimelineWidget::onHorizontalScrollChanged);
            }
            if (m_timelineScrollArea->verticalScrollBar()) {
                connect(m_timelineScrollArea->verticalScrollBar(), &QScrollBar::valueChanged,
                        this, &TimelineWidget::onVerticalScrollChanged);
            }
        }
    });
}

void TimelineWidget::setAAFReader(AAFReader *reader)
{
    m_aafReader = reader;
}

void TimelineWidget::setAudioFileManager(AudioFileManager *manager)
{
    // Simply store the manager - avoid any complex operations during startup
    m_audioFileManager = manager;

    // Audio playback manager integration will be handled later when needed
    // This prevents startup crashes while preserving functionality
}

bool TimelineWidget::preserveOriginalTiming() const
{
    return m_preserveTimingCheckBox ? m_preserveTimingCheckBox->isChecked() : true;
}

void TimelineWidget::setFrameRate(double frameRate)
{
    m_frameRate = frameRate;
    m_ruler->setFrameRate(frameRate);
    m_transportControls->setFrameRate(frameRate);
}

void TimelineWidget::setTimecodeFormat(int format)
{
    m_timecodeFormat = format;
    m_ruler->setTimecodeFormat(format);
    m_transportControls->setTimecodeFormat(format);
}

void TimelineWidget::refreshTimeline()
{
    updateTrackDisplay();
    updateTimelineSize();
    update();
}

void TimelineWidget::forceRefresh()
{
    qDebug() << "TimelineWidget::forceRefresh: Forcing complete timeline refresh";

    // Force immediate update of all components
    updateTrackDisplay();
    updateTimelineSize();

    // Force repaint of all widgets
    if (m_ruler) {
        m_ruler->update();
        m_ruler->repaint();
    }

    for (auto content : m_trackContents) {
        content->update();
        content->repaint();
    }

    for (auto header : m_trackHeaders) {
        header->update();
        header->repaint();
    }

    // Force scroll area update
    if (m_timelineScrollArea) {
        m_timelineScrollArea->update();
        m_timelineScrollArea->repaint();
        if (m_timelineScrollArea->viewport()) {
            m_timelineScrollArea->viewport()->update();
            m_timelineScrollArea->viewport()->repaint();
        }
    }

    update();
    repaint();

    qDebug() << "TimelineWidget::forceRefresh: Refresh completed";
}

void TimelineWidget::setAudioPlaybackManager(AudioPlaybackManager *manager)
{
    m_audioPlaybackManager = manager;
    if (m_audioPlaybackManager) {
        // Deferred initialization to prevent startup issues
        QTimer::singleShot(100, this, &TimelineWidget::initializeAudioComponents);
    }
}

void TimelineWidget::loadTimelineData(const QVariantList &regions, const QVariantList &tracks)
{
    qDebug() << "=== TIMELINE WIDGET loadTimelineData CALLED ===";
    qDebug() << "Regions count:" << regions.size();
    qDebug() << "Tracks count:" << tracks.size();

    // Clear existing data
    m_tracks.clear();
    m_regions.clear();

    // Clear existing UI
    for (auto header : m_trackHeaders) {
        header->deleteLater();
    }
    for (auto content : m_trackContents) {
        content->deleteLater();
    }
    m_trackHeaders.clear();
    m_trackContents.clear();

    // Create tracks from regions if no tracks provided
    if (tracks.isEmpty()) {
        qDebug() << "TIMELINE DEBUG: No tracks provided, creating from regions";
        createTracksFromRegions(regions);
    } else {
        qDebug() << "TIMELINE DEBUG: Loading provided tracks";
        // Load provided tracks
        for (const auto &trackData : tracks) {
            QVariantMap trackMap = trackData.toMap();
            TimelineTrack track;
            track.name = trackMap.value("name").toString();
            QString originalName = trackMap.value("originalName").toString();
            track.originalName = originalName.isEmpty() ? track.name : originalName;
            track.trackNumber = trackMap.value("trackNumber", 0).toInt();
            track.type = trackMap.value("type").toString();
            track.visible = trackMap.value("visible", true).toBool();
            track.muted = trackMap.value("muted", false).toBool();
            track.solo = trackMap.value("solo", false).toBool();
            track.color = getColorForContentType(track.type);
            m_tracks.append(track);
            qDebug() << "TIMELINE DEBUG: Created track:" << track.name
                     << "originalName:" << track.originalName
                     << "trackNumber:" << track.trackNumber;
        }
    }

    // Load regions
    QByteArray disableMock = qgetenv("WAAFER_DISABLE_MOCK_DATA");
    bool mockDisabled = (disableMock == "1" || disableMock.toLower() == "true");

    for (const auto &regionData : regions) {
        QVariantMap regionMap = regionData.toMap();

        TimelineRegion region;
        region.id = regionMap.value("regionId", regionMap.value("id")).toString();

        // Extract both name sources for toggle functionality
        QString sourceAName = regionMap.value("name").toString();  // Source A: AAF clip names
        QString sourceBName;  // Source B: Descriptions/metadata
        QString audioFilePath = regionMap.value("audioFilePath", regionMap.value("essenceFilePath")).toString();

        // Source A: File-based names (UnnamedA01.66EAEC966EAEBE1A format)
        if (sourceAName.isEmpty() || sourceAName == "Clip" || sourceAName.startsWith("region_")) {
            if (!audioFilePath.isEmpty()) {
                QFileInfo fileInfo(audioFilePath);
                sourceAName = fileInfo.baseName(); // Use filename without extension
            }
        }

        // Source B: Try to extract descriptive names from metadata or essence file names
        QVariantMap metadata = regionMap.value("metadata").toMap();
        sourceBName = metadata.value("description").toString();
        if (sourceBName.isEmpty()) {
            sourceBName = metadata.value("comment").toString();
        }
        if (sourceBName.isEmpty()) {
            // Try essence file name as alternative
            QString essenceFileName = regionMap.value("essenceFileName").toString();
            if (!essenceFileName.isEmpty() && essenceFileName != sourceAName) {
                sourceBName = essenceFileName;
            }
        }

        // Fallback names
        if (sourceAName.isEmpty()) {
            sourceAName = region.id;
        }
        if (sourceBName.isEmpty()) {
            sourceBName = sourceAName;  // Use Source A as fallback
        }

        region.name = sourceAName;
        region.alternativeName = sourceBName;
        region.trackName = regionMap.value("trackName", regionMap.value("track")).toString();

        // Extract enhanced stereo channel information from LibAAF
        region.channelNumber = regionMap.value("channelNumber", 0).toInt();
        region.stereoGroupId = regionMap.value("stereoGroupId", QString()).toString();

        // Get channel type information
        QString channelType = regionMap.value("channelType", QString()).toString();

        // If LibAAF didn't provide stereo info, fall back to track-based detection
        int trackNumber = regionMap.value("trackNumber", -1).toInt();
        if (region.stereoGroupId.isEmpty() && trackNumber > 0) {
            // Group consecutive tracks as stereo pairs (1-2, 3-4, 5-6, etc.)
            int stereoGroupNumber = (trackNumber - 1) / 2 + 1;
            region.stereoGroupId = QString("StereoGroup_%1").arg(stereoGroupNumber);

            // Assign channel number if not already set
            if (region.channelNumber == 0) {
                region.channelNumber = ((trackNumber - 1) % 2) + 1;  // 1 for left, 2 for right
            }
        }

        // Store additional stereo information in metadata for debugging
        if (!channelType.isEmpty()) {
            region.metadata["channelType"] = channelType;
        }
        region.metadata["originalTrackNumber"] = trackNumber;

        // Skip mock regions when mock data is disabled
        if (mockDisabled && (region.id.contains("❌") || region.id.contains("MOCK_") || region.id.contains("FAKE_") ||
                            region.name.contains("❌") || region.name.contains("MOCK_") || region.name.contains("FAKE_") ||
                            region.trackName.contains("❌") || region.trackName.contains("MOCK_") || region.trackName.contains("FAKE_"))) {
            continue;
        }

        // Use correct field names from LibAAF
        region.startTime = regionMap.value("position", regionMap.value("startTime", 0.0)).toDouble();
        region.duration = regionMap.value("length", regionMap.value("duration", 1.0)).toDouble();
        region.contentType = regionMap.value("contentType", "Unknown").toString();
        region.speakerId = regionMap.value("speakerId").toString();
        region.confidence = regionMap.value("confidence", 0.0).toDouble();
        region.audioFilePath = regionMap.value("essenceFilePath", regionMap.value("audioFilePath")).toString();
        region.color = getColorForContentType(region.contentType);
        region.metadata = regionMap;

        m_regions[region.id] = region;

        // Add region to appropriate track
        bool regionAssigned = false;

        // Debug: Log region details for assignment
        qDebug() << "TRACK-REGION ASSIGNMENT DEBUG: Processing region" << region.id;
        qDebug() << "  Region trackName:" << region.trackName;
        qDebug() << "  Region contentType:" << region.contentType;
        qDebug() << "  Available tracks:" << m_tracks.size();

        for (auto &track : m_tracks) {
            qDebug() << "    Track name:" << track.name << "originalName:" << track.originalName << "type:" << track.type;

            // First priority: exact original track name match (from AAF)
            if (!region.trackName.isEmpty() && track.originalName == region.trackName) {
                qDebug() << "    -> MATCH: First priority (originalName match)";
                track.regions.append(region);
                regionAssigned = true;
                break;
            }
            // Second priority: track number match (from LibAAF trackNumber field)
            else if (track.trackNumber >= 0 && regionMap.contains("trackNumber") &&
                     track.trackNumber == regionMap.value("trackNumber").toInt()) {
                qDebug() << "    -> MATCH: Second priority (track number match)";
                track.regions.append(region);
                regionAssigned = true;
                break;
            }
            // Third priority: content type match
            else if (track.type == region.contentType && region.contentType != "Unknown") {
                qDebug() << "    -> MATCH: Third priority (content type match)";
                track.regions.append(region);
                regionAssigned = true;
                break;
            }
        }

        if (!regionAssigned) {
            qDebug() << "  -> NO MATCH: Region" << region.id << "could not be assigned to any track";
        }

        // If no matching track found, assign to first track
        if (!regionAssigned && !m_tracks.isEmpty()) {
            m_tracks.first().regions.append(region);
        }
    }

    // Calculate duration
    m_duration = 0.0;
    for (const auto &region : m_regions) {
        m_duration = qMax(m_duration, region.startTime + region.duration);
    }
    m_duration = qMax(m_duration, 300.0); // Minimum 5 minutes

    // Update UI components
    m_ruler->setDuration(m_duration);
    m_transportControls->setDuration(m_duration);

    // Update track display
    updateTrackDisplay();
    updateTimelineSize();

    qDebug() << "TimelineWidget::loadTimelineData: Loaded" << m_tracks.size() << "tracks," << m_regions.size() << "regions, duration:" << m_duration;

    // Force scroll area update
    if (m_timelineScrollArea) {
        m_timelineScrollArea->setVisible(true);
        m_timelineScrollArea->update();
        m_timelineScrollArea->repaint();
    }

    // Update all track content widgets
    for (auto content : m_trackContents) {
        if (content) {
            content->setVisible(true);
            content->update();
            content->repaint();
        }
    }

    // Force this widget to update
    setVisible(true);
    update();
    repaint();

    qDebug() << "TimelineWidget::loadTimelineData: UI update completed";
}

void TimelineWidget::createTracksFromRegions(const QVariantList &regions)
{
    // Use QMap to preserve track order by trackIndex from AAF
    QMap<int, QString> orderedTrackNames;
    QSet<QString> contentTypes;
    QSet<QString> speakers;

    // First, collect track names with their AAF order indices
    for (const auto &regionData : regions) {
        QVariantMap regionMap = regionData.toMap();
        // Use correct field names from LibAAF
        QString trackName = regionMap.value("trackName", regionMap.value("track")).toString();
        int trackIndex = regionMap.value("trackNumber", regionMap.value("trackIndex", -1)).toInt();
        QString contentType = regionMap.value("contentType", "Unknown").toString();
        QString speakerId = regionMap.value("speakerId").toString();

        // Collect actual track names from AAF with their original order
        if (!trackName.isEmpty()) {
            if (trackIndex >= 0) {
                // Use AAF track index to preserve order
                orderedTrackNames[trackIndex] = trackName;
            } else {
                // Fallback: assign a high index if trackIndex is missing
                int fallbackIndex = 1000 + orderedTrackNames.size();
                orderedTrackNames[fallbackIndex] = trackName;
            }
        }

        // Also collect content types for classification-based tracks
        contentTypes.insert(contentType);
        if (!speakerId.isEmpty() && contentType == "Speech") {
            speakers.insert(speakerId);
        }
    }

    // Create tracks based on AAF track names in their original order (preferred)
    if (!orderedTrackNames.isEmpty()) {
        // QMap automatically sorts by key (trackIndex), preserving AAF order
        int displayTrackNumber = 1;
        for (auto it = orderedTrackNames.begin(); it != orderedTrackNames.end(); ++it) {
            const QString &originalTrackName = it.value();

            // Skip mock track names when mock data is disabled
            QByteArray disableMock = qgetenv("WAAFER_DISABLE_MOCK_DATA");
            if ((disableMock == "1" || disableMock.toLower() == "true") &&
                (originalTrackName.contains("❌") || originalTrackName.contains("MOCK_") || originalTrackName.contains("FAKE_"))) {
                continue;
            }

            TimelineTrack track;
            // Add track numbering: "1. Track Name"
            track.name = QString("%1. %2").arg(displayTrackNumber).arg(originalTrackName);
            track.originalName = originalTrackName; // Store original name for matching
            track.trackNumber = it.key(); // Store AAF track number
            track.type = "Audio"; // Default type for AAF tracks
            track.color = getColorForContentType("Audio");
            m_tracks.append(track);

            displayTrackNumber++;
        }
    }

    // If no valid tracks from AAF, fall back to content-type based tracks
    if (m_tracks.isEmpty() && !contentTypes.isEmpty()) {
        for (const QString &contentType : contentTypes) {
            if (contentType == "Speech") {
                // Create separate tracks for each speaker
                for (const QString &speaker : speakers) {
                    TimelineTrack track;
                    track.name = QString("Speech_%1").arg(speaker);
                    track.type = "Speech";
                    track.color = getColorForContentType("Speech");
                    m_tracks.append(track);
                }
            } else if (contentType != "Unknown") {
                TimelineTrack track;
                track.name = contentType;
                track.type = contentType;
                track.color = getColorForContentType(contentType);
                m_tracks.append(track);
            }
        }
    }

    // If still no tracks, create a default track
    if (m_tracks.isEmpty()) {
        TimelineTrack track;
        track.name = "Audio Track 1";
        track.type = "Audio";
        track.color = getColorForContentType("Audio");
        m_tracks.append(track);
    }
}

QColor TimelineWidget::getColorForContentType(const QString &contentType) const
{
    if (contentType == "Speech") return QColor(100, 150, 255);
    if (contentType == "Music") return QColor(255, 150, 100);
    if (contentType == "SFX") return QColor(150, 255, 100);
    if (contentType == "Ambience") return QColor(200, 200, 100);
    if (contentType == "Silence") return QColor(100, 100, 100);
    return QColor(150, 150, 150); // Unknown
}

// Removed - replaced by updateTrackDisplay()

// Removed - replaced by updateTrackDisplay()

void TimelineWidget::updateRegionClassification(const QString &regionId, const QVariantMap &classification)
{
    if (m_regions.contains(regionId)) {
        TimelineRegion &region = m_regions[regionId];
        region.contentType = classification.value("contentType").toString();
        region.speakerId = classification.value("speakerId").toString();
        region.confidence = classification.value("confidence").toDouble();
        region.color = getColorForContentType(region.contentType);

        // Update the region in tracks
        for (auto &track : m_tracks) {
            for (auto &trackRegion : track.regions) {
                if (trackRegion.id == regionId) {
                    trackRegion = region;
                    break;
                }
            }
        }

        // Update UI
        for (auto content : m_trackContents) {
            content->update();
        }

        // Update metadata panel if this region is selected
        if (m_selectedRegionId == regionId) {
            m_metadataPanel->setRegion(region);
        }
    }
}

void TimelineWidget::zoomIn()
{
    m_zoom = qMin(m_zoom * 1.5, 10.0);
    updateZoom();
}

void TimelineWidget::zoomOut()
{
    m_zoom = qMax(m_zoom / 1.5, 0.1);
    updateZoom();
}

void TimelineWidget::fitToWindow()
{
    if (m_duration > 0) {
        int availableWidth = m_timelineScrollArea->viewport()->width() - 50;
        m_zoom = availableWidth / (m_duration * 100.0);
        m_zoom = qMax(m_zoom, 0.1);
        updateZoom();
    }
}

void TimelineWidget::updateZoom()
{
    // Calculate new width with bounds checking
    int newWidth = static_cast<int>(m_duration * 100.0 * m_zoom);
    newWidth = qMax(newWidth, 100); // Minimum width
    newWidth = qMin(newWidth, 100000); // Maximum width to prevent memory issues

    // Update ruler with proper size constraints
    if (m_ruler) {
        m_ruler->setZoom(m_zoom);
        m_ruler->setFixedWidth(newWidth);
        m_ruler->updateGeometry();
    }

    // Update all track contents with proper containment
    for (auto content : m_trackContents) {
        content->setZoom(m_zoom);

        // Set fixed width to prevent overflow
        content->setFixedWidth(newWidth);
        content->updateGeometry();

        // Use update() instead of repaint() for better performance
        content->update();
    }

    // Update timeline container size with proper constraints
    if (m_timelineContainer) {
        m_timelineContainer->setFixedWidth(newWidth);
        m_timelineContainer->updateGeometry();

        // Ensure container stays within scroll area bounds
        QSize containerSize = m_timelineContainer->sizeHint();
        m_timelineContainer->resize(containerSize);
    }

    // Update scroll area to handle new content size
    if (m_timelineScrollArea) {
        m_timelineScrollArea->updateGeometry();
        m_timelineScrollArea->update();

        // Ensure scroll area viewport is properly updated
        if (m_timelineScrollArea->viewport()) {
            m_timelineScrollArea->viewport()->update();
        }
    }
}

void TimelineWidget::playPause()
{
    if (!m_audioPlaybackManager) {
        return;
    }

    if (m_audioPlaybackManager->isPlaying()) {
        m_audioPlaybackManager->pause();
    } else {
        m_audioPlaybackManager->play();
    }
}

void TimelineWidget::stop()
{
    if (m_audioPlaybackManager) {
        m_audioPlaybackManager->stop();
    }

    m_transportControls->stop();
    m_playheadPosition = 0.0;
    onPlayheadMoved(0.0);
}

void TimelineWidget::onPlayheadMoved(double position)
{
    m_playheadPosition = position;
    m_ruler->setPlayheadPosition(position);
    m_transportControls->setPlayheadPosition(position);

    for (auto content : m_trackContents) {
        content->setPlayheadPosition(position);
    }
}

void TimelineWidget::onRegionSelected(const QString &regionId)
{
    m_selectedRegionId = regionId;

    // Update selection in all tracks
    for (auto &track : m_tracks) {
        for (auto &region : track.regions) {
            region.selected = (region.id == regionId);
        }
    }

    // Update UI
    for (auto content : m_trackContents) {
        content->update();
    }

    // Update metadata panel
    if (m_regions.contains(regionId)) {
        m_metadataPanel->setRegion(m_regions[regionId]);

        if (m_audioPlaybackManager) {
            TimelineRegion region = m_regions[regionId];
            // Set playhead to region start
            m_audioPlaybackManager->setPosition(region.startTime);
        }
    }

    emit regionSelected(regionId);
}

void TimelineWidget::onTrackMuteToggled(const QString &trackName, bool muted)
{
    for (auto &track : m_tracks) {
        if (track.name == trackName) {
            track.muted = muted;
            break;
        }
    }

    // Update track contents
    for (auto content : m_trackContents) {
        content->update();
    }
}

void TimelineWidget::onTrackSoloToggled(const QString &trackName, bool solo)
{
    for (auto &track : m_tracks) {
        if (track.name == trackName) {
            track.solo = solo;
        } else if (solo) {
            track.solo = false; // Only one track can be soloed
        }
    }

    // Update track headers and contents
    for (int i = 0; i < m_tracks.size(); ++i) {
        if (i < m_trackHeaders.size()) {
            m_trackHeaders[i]->updateTrack(m_tracks[i]);
        }
        if (i < m_trackContents.size()) {
            m_trackContents[i]->update();
        }
    }
}

void TimelineWidget::onTrackSelectionToggled(const QString &trackName, bool selected)
{
    // Update track selection state
    for (auto &track : m_tracks) {
        if (track.name == trackName) {
            track.selected = selected;
            break;
        }
    }

    // Track selection is now handled directly in track headers

    qDebug() << "Track" << trackName << "selection changed to:" << selected;
}

void TimelineWidget::onRegionContextMenu(const QString &regionId, const QPoint &position)
{
    QMenu contextMenu;

    QAction *playAction = contextMenu.addAction("Play Region");
    QAction *editAction = contextMenu.addAction("Edit Metadata");
    contextMenu.addSeparator();
    QAction *deleteAction = contextMenu.addAction("Delete Region");

    QAction *selectedAction = contextMenu.exec(position);

    if (selectedAction == playAction) {
        // Play the selected region
        if (m_audioPlaybackManager && m_regions.contains(regionId)) {
            TimelineRegion region = m_regions[regionId];

            QVariantMap regionInfo;
            regionInfo["id"] = region.id;
            regionInfo["name"] = region.name;
            regionInfo["startTime"] = region.startTime;
            regionInfo["duration"] = region.duration;
            regionInfo["audioFilePath"] = region.audioFilePath;

            m_audioPlaybackManager->playRegion(regionInfo);
        }
        emit playbackRequested(regionId);
    } else if (selectedAction == editAction) {
        onRegionSelected(regionId);
    } else if (selectedAction == deleteAction) {
        // Handle region deletion
        // This would need to be implemented based on the application's needs
    }
}





// Track Selection Implementation





QStringList TimelineWidget::getSelectedTrackNames() const
{
    QStringList selectedTracks;
    for (const auto &track : m_tracks) {
        if (track.selected) {
            selectedTracks.append(track.name);
        }
    }
    return selectedTracks;
}

QStringList TimelineWidget::getSelectedRegionIds() const
{
    QStringList selectedRegions;
    for (const auto &region : m_regions) {
        if (region.selected) {
            selectedRegions.append(region.id);
        }
    }
    return selectedRegions;
}

QVariantList TimelineWidget::getSelectedRegions() const
{
    QVariantList selectedRegions;

    for (const auto &track : m_tracks) {
        if (!track.selected) continue; // Skip unselected tracks

        for (const auto &region : track.regions) {
            if (region.selected) {
                QVariantMap regionData;
                regionData["id"] = region.id;
                regionData["name"] = region.name;
                regionData["trackName"] = region.trackName;
                regionData["startTime"] = region.startTime;
                regionData["duration"] = region.duration;
                regionData["contentType"] = region.contentType;
                regionData["speakerId"] = region.speakerId;
                regionData["confidence"] = region.confidence;
                regionData["audioFilePath"] = region.audioFilePath;
                regionData["metadata"] = region.metadata;
                selectedRegions.append(regionData);
            }
        }
    }

    return selectedRegions;
}

void TimelineWidget::setTrackSelection(const QString &trackName, bool selected)
{
    for (int i = 0; i < m_tracks.size(); ++i) {
        if (m_tracks[i].name == trackName) {
            m_tracks[i].selected = selected;
            // Update corresponding track header
            if (i < m_trackHeaders.size()) {
                m_trackHeaders[i]->setSelected(selected);
            }
            break;
        }
    }
    syncTrackSelection();
}

void TimelineWidget::setAllTracksSelected(bool selected)
{
    for (auto &track : m_tracks) {
        track.selected = selected;
    }
    for (auto header : m_trackHeaders) {
        header->setSelected(selected);
    }
    syncTrackSelection();
}





void TimelineWidget::setTimeRangeHighlight(double startTime, double endTime, bool enabled)
{
    m_timeRangeHighlightEnabled = enabled;
    m_timeRangeStartTime = startTime;
    m_timeRangeEndTime = endTime;

    // Update all track content widgets to show the highlight
    for (auto trackContent : m_trackContents) {
        trackContent->update(); // Trigger repaint
    }

    qDebug() << "Time range highlight set:" << enabled << "from" << startTime << "to" << endTime;
}

void TimelineWidget::clearTimeRangeHighlight()
{
    setTimeRangeHighlight(0.0, 0.0, false);
}

// Time Range Selection Implementation
void TimelineWidget::startTimeRangeSelection(double startTime)
{
    m_timeRangeSelectionActive = true;
    m_timeRangeSelectionStart = startTime;
    m_timeRangeSelectionEnd = startTime;

    // Update visual feedback
    setTimeRangeHighlight(startTime, startTime, true);

    qDebug() << "Started time range selection at:" << startTime;
}

void TimelineWidget::updateTimeRangeSelection(double endTime)
{
    if (!m_timeRangeSelectionActive) return;

    m_timeRangeSelectionEnd = endTime;

    // Ensure start is always before end
    double start = qMin(m_timeRangeSelectionStart, m_timeRangeSelectionEnd);
    double end = qMax(m_timeRangeSelectionStart, m_timeRangeSelectionEnd);

    // Update visual feedback
    setTimeRangeHighlight(start, end, true);
}

void TimelineWidget::finishTimeRangeSelection()
{
    if (!m_timeRangeSelectionActive) return;

    // Ensure start is always before end
    double start = qMin(m_timeRangeSelectionStart, m_timeRangeSelectionEnd);
    double end = qMax(m_timeRangeSelectionStart, m_timeRangeSelectionEnd);

    m_timeRangeSelectionActive = false;

    // Only emit if we have a meaningful selection (more than 0.1 seconds)
    if (qAbs(end - start) > 0.1) {
        emit timeRangeSelectionChanged(start, end);
        qDebug() << "Time range selection finished:" << start << "to" << end;
    } else {
        // Clear selection if too small
        clearTimeRangeHighlight();
        qDebug() << "Time range selection cancelled (too small)";
    }
}

void TimelineWidget::cancelTimeRangeSelection()
{
    if (!m_timeRangeSelectionActive) return;

    m_timeRangeSelectionActive = false;
    clearTimeRangeHighlight();

    qDebug() << "Time range selection cancelled";
}

QPair<double, double> TimelineWidget::getSelectedTimeRange() const
{
    if (m_timeRangeHighlightEnabled) {
        return QPair<double, double>(m_timeRangeStartTime, m_timeRangeEndTime);
    }
    return QPair<double, double>(0.0, 0.0);
}

// New Timeline Widget Methods

void TimelineWidget::updateTrackDisplay()
{
    qDebug() << "TimelineWidget::updateTrackDisplay: Starting with" << m_tracks.size() << "tracks";

    // Clear existing track widgets
    for (auto header : m_trackHeaders) {
        header->deleteLater();
    }
    for (auto content : m_trackContents) {
        content->deleteLater();
    }
    m_trackHeaders.clear();
    m_trackContents.clear();

    // Clear existing layouts
    if (m_trackHeadersLayout) {
        QLayoutItem *item;
        while ((item = m_trackHeadersLayout->takeAt(0)) != nullptr) {
            if (item->widget()) {
                item->widget()->deleteLater();
            }
            delete item;
        }
    }

    if (m_timelineLayout) {
        QLayoutItem *item;
        while ((item = m_timelineLayout->takeAt(0)) != nullptr) {
            if (item->widget()) {
                item->widget()->deleteLater();
            }
            delete item;
        }
    }

    // Create track widgets using new fixed header structure
    for (int i = 0; i < m_tracks.size(); ++i) {
        const TimelineTrack &track = m_tracks[i];

        // Create fixed header (in synchronized scroll area)
        TrackHeader *header = new TrackHeader(track);
        header->setFixedHeight(TRACK_HEIGHT);
        header->setFixedWidth(150);
        // Clipping handled by parent container
        m_trackHeaders.append(header);
        m_trackHeadersLayout->addWidget(header);

        // Connect header signals
        connect(header, &TrackHeader::selectionToggled, this, &TimelineWidget::onTrackSelectionToggled);
        connect(header, &TrackHeader::muteToggled, this, &TimelineWidget::onTrackMuteToggled);
        connect(header, &TrackHeader::soloToggled, this, &TimelineWidget::onTrackSoloToggled);

        // Create scrollable content
        TrackContent *content = new TrackContent(track);
        content->setFixedHeight(TRACK_HEIGHT);
        // Clipping handled by scroll area
        content->setZoom(m_zoom);
        content->setDuration(m_duration);
        content->setPlayheadPosition(m_playheadPosition);
        content->setTimelineWidget(this);
        content->setTimeRangeHighlight(m_timeRangeStartTime, m_timeRangeEndTime, m_timeRangeHighlightEnabled);

        // Set display options
        content->setShowWaveforms(m_showWaveforms);
        content->setShowRegionNames(m_showRegionNames);
        content->setShowConfidence(m_showConfidence);

        m_trackContents.append(content);
        m_timelineLayout->addWidget(content);

        // Connect content signals
        connect(content, &TrackContent::regionSelected, this, &TimelineWidget::onRegionSelected);
        connect(content, &TrackContent::zoomRequested, this, &TimelineWidget::onZoomRequested);
        connect(content, &TrackContent::regionContextMenu, this, &TimelineWidget::onRegionContextMenu);
    }

    qDebug() << "TimelineWidget::updateTrackDisplay: Created" << m_trackHeaders.size() << "headers and" << m_trackContents.size() << "content widgets";
}

void TimelineWidget::updateTimelineSize()
{
    if (m_trackHeadersContainer && m_timelineContainer) {
        int totalHeight = m_tracks.size() * TRACK_HEIGHT;
        int timelineWidth = qMax(1000, static_cast<int>(m_duration * 100.0 * m_zoom));

        qDebug() << "TimelineWidget::updateTimelineSize: totalHeight=" << totalHeight << "timelineWidth=" << timelineWidth;

        // Update track headers container size to match timeline content height
        m_trackHeadersContainer->setFixedSize(150, totalHeight);
        m_trackHeadersContainer->updateGeometry();

        // Update scrollable timeline container size with strict constraints
        m_timelineContainer->setFixedSize(timelineWidth, totalHeight);
        m_timelineContainer->updateGeometry();

        // Update ruler width to match timeline content
        if (m_ruler) {
            m_ruler->setMinimumWidth(timelineWidth);
            m_ruler->setFixedWidth(timelineWidth);
        }

        // Force both scroll areas to update their scrollbars
        if (m_timelineScrollArea) {
            m_timelineScrollArea->updateGeometry();
            m_timelineScrollArea->update();
        }

        if (m_trackHeadersScrollArea) {
            m_trackHeadersScrollArea->updateGeometry();
            m_trackHeadersScrollArea->update();
        }

        qDebug() << "TimelineWidget::updateTimelineSize: Updated container sizes - timeline:" << timelineWidth << "x" << totalHeight << "headers: 150x" << totalHeight;
    }
}

void TimelineWidget::syncTrackSelection()
{
    QStringList selectedTracks = getSelectedTrackNames();
    emit trackSelectionChanged(selectedTracks);
}

void TimelineWidget::initializeAudioComponents()
{
    if (m_audioPlaybackManager && m_audioFileManager) {
        m_audioPlaybackManager->setAudioFileManager(m_audioFileManager);

        // Connect audio signals
        connect(m_audioPlaybackManager, &AudioPlaybackManager::positionChanged,
                this, &TimelineWidget::onAudioPlaybackPositionChanged);
        connect(m_audioPlaybackManager, &AudioPlaybackManager::durationChanged,
                this, &TimelineWidget::onAudioPlaybackDurationChanged);
        connect(m_audioPlaybackManager, &AudioPlaybackManager::isPlayingChanged,
                this, &TimelineWidget::onAudioPlaybackStateChanged);
    }
}

// Slot Implementations

void TimelineWidget::onZoomRequested(double factor)
{
    double newZoom = m_zoom * factor;
    newZoom = qMax(0.1, qMin(10.0, newZoom));

    if (qAbs(newZoom - m_zoom) > 0.01) {
        m_zoom = newZoom;
        updateZoom();
    }
}

void TimelineWidget::onTransportPlayRequested()
{
    emit playbackRequested("");
    m_transportControls->play();
}

void TimelineWidget::onTransportPauseRequested()
{
    m_transportControls->pause();
}

void TimelineWidget::onTransportStopRequested()
{
    m_transportControls->stop();
    onPlayheadMoved(0.0);
}

void TimelineWidget::onTransportPositionChanged(double position)
{
    onPlayheadMoved(position);
}

void TimelineWidget::onFrameRateChanged(double frameRate)
{
    setFrameRate(frameRate);
}

void TimelineWidget::onTimecodeFormatChanged(int format)
{
    setTimecodeFormat(format);
}

void TimelineWidget::onToolbarZoomIn()
{
    zoomIn();
}

void TimelineWidget::onToolbarZoomOut()
{
    zoomOut();
}

void TimelineWidget::onToolbarFitToWindow()
{
    fitToWindow();
}

void TimelineWidget::onToolbarTrackSelection(bool selectAll)
{
    setAllTracksSelected(selectAll);
}

void TimelineWidget::onShowWaveformsToggled(bool show)
{
    qDebug() << "=== WAVEFORM TOGGLE SIGNAL RECEIVED ===" << "show:" << show;
    qDebug() << "Current tracks count:" << m_tracks.size();
    qDebug() << "Current track contents count:" << m_trackContents.size();
    qDebug() << "Previous m_showWaveforms state:" << m_showWaveforms;

    m_showWaveforms = show;
    qDebug() << "New m_showWaveforms state:" << m_showWaveforms;

    for (int i = 0; i < m_trackContents.size(); ++i) {
        auto content = m_trackContents[i];
        if (content) {
            qDebug() << "Setting waveforms for track content" << i << "to:" << show;
            content->setShowWaveforms(show);
        } else {
            qDebug() << "Track content" << i << "is null!";
        }
    }

    // If waveforms are being enabled, start calculating waveform data for visible regions
    if (show) {
        qDebug() << "Waveforms enabled - starting waveform calculation for visible regions";
        int regionCount = 0;
        int regionsWithAudio = 0;

        for (const auto &track : m_tracks) {
            qDebug() << "Track:" << track.name << "regions:" << track.regions.size();
            for (const auto &region : track.regions) {
                regionCount++;
                qDebug() << "Region:" << region.id << "audioFilePath:" << region.audioFilePath;
                if (!region.audioFilePath.isEmpty()) {
                    regionsWithAudio++;
                    qDebug() << "Starting waveform calculation for region:" << region.id;
                    calculateWaveformData(region.id, region.audioFilePath);
                } else {
                    qDebug() << "Region has no audio file path:" << region.id;
                }
            }
        }

        qDebug() << "Total regions:" << regionCount << "with audio paths:" << regionsWithAudio;
    } else {
        qDebug() << "Waveforms disabled - stopping waveform calculations";
        // Note: We keep calculated data in cache for when waveforms are re-enabled
    }

    // Force update of all track contents
    for (auto content : m_trackContents) {
        if (content) {
            content->update();
        }
    }
}

void TimelineWidget::onShowRegionNamesToggled(bool show)
{
    m_showRegionNames = show;
    for (auto content : m_trackContents) {
        content->setShowRegionNames(show);
    }
}

void TimelineWidget::onToggleRegionNameSource()
{
    m_useAlternativeRegionNames = !m_useAlternativeRegionNames;

    // Update the tooltip to show current source
    if (m_toolbar && m_toolbar->getToggleRegionNameSourceAction()) {
        QString tooltip = m_useAlternativeRegionNames ?
            "Region Names: Source B (Descriptions)" :
            "Region Names: Source A (File Names)";
        m_toolbar->getToggleRegionNameSourceAction()->setToolTip(tooltip);
        m_toolbar->getToggleRegionNameSourceAction()->setChecked(m_useAlternativeRegionNames);
    }

    // Force refresh of all track contents to show new names
    for (auto content : m_trackContents) {
        content->update();
    }

    qDebug() << "TimelineWidget: Toggled region name source to"
             << (m_useAlternativeRegionNames ? "Source B (Descriptions)" : "Source A (File Names)");
}

void TimelineWidget::onShowConfidenceToggled(bool show)
{
    m_showConfidence = show;
    for (auto content : m_trackContents) {
        content->update();
    }
}

void TimelineWidget::onAudioPlaybackPositionChanged(double position)
{
    onPlayheadMoved(position);
}

void TimelineWidget::onAudioPlaybackDurationChanged(double duration)
{
    if (duration > 0) {
        m_duration = qMax(m_duration, duration);
        m_ruler->setDuration(m_duration);
        m_transportControls->setDuration(m_duration);
        updateTimelineSize();
    }
}

void TimelineWidget::onAudioPlaybackStateChanged(bool isPlaying)
{
    if (isPlaying) {
        m_transportControls->play();
    } else {
        m_transportControls->pause();
    }
}

void TimelineWidget::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);

    // Update timeline size when widget is resized
    updateTimelineSize();

    // Ensure proper redraw of all components
    if (m_ruler) {
        m_ruler->update();
    }

    for (auto content : m_trackContents) {
        content->update();
    }

    for (auto header : m_trackHeaders) {
        header->update();
    }
}

void TimelineWidget::onHorizontalScrollChanged(int value)
{
    // Synchronize ruler position with timeline scroll
    if (m_ruler && m_timelineScrollArea) {
        // Calculate the scroll offset
        QScrollBar *hScrollBar = m_timelineScrollArea->horizontalScrollBar();
        if (hScrollBar) {
            int maxScroll = hScrollBar->maximum();
            int rulerWidth = m_ruler->width();
            int viewportWidth = m_timelineScrollArea->viewport()->width();

            if (maxScroll > 0 && rulerWidth > viewportWidth) {
                // Calculate proportional offset for ruler
                double scrollRatio = static_cast<double>(value) / maxScroll;
                int rulerOffset = static_cast<int>(scrollRatio * (rulerWidth - viewportWidth));

                // Apply offset to ruler (this would need a custom ruler widget with scroll support)
                // For now, just trigger an update
                m_ruler->update();
            }
        }
    }
}

void TimelineWidget::onVerticalScrollChanged(int value)
{
    qDebug() << "TimelineWidget::onVerticalScrollChanged: value=" << value;

    // Synchronize track headers scroll area with main timeline scroll area
    if (m_timelineScrollArea && m_trackHeadersScrollArea) {
        // Get the vertical scroll bar from the main timeline
        QScrollBar *mainVScrollBar = m_timelineScrollArea->verticalScrollBar();
        QScrollBar *headerVScrollBar = m_trackHeadersScrollArea->verticalScrollBar();

        if (mainVScrollBar && headerVScrollBar) {
            // Temporarily disconnect to prevent infinite loop
            headerVScrollBar->blockSignals(true);

            // Synchronize the header scroll position with main scroll position
            headerVScrollBar->setValue(value);

            // Re-enable signals
            headerVScrollBar->blockSignals(false);

            qDebug() << "Synchronized header scroll to value:" << value;
        }

        // Force updates to prevent visual artifacts
        if (m_timelineScrollArea->viewport()) {
            m_timelineScrollArea->viewport()->update();
        }

        if (m_trackHeadersScrollArea->viewport()) {
            m_trackHeadersScrollArea->viewport()->update();
        }

        // Update all track widgets for proper rendering
        for (auto content : m_trackContents) {
            if (content) {
                content->update();
            }
        }

        for (auto header : m_trackHeaders) {
            if (header) {
                header->update();
            }
        }
    }
}

void TimelineWidget::calculateWaveformData(const QString &regionId, const QString &audioFilePath)
{
    qDebug() << "=== CALCULATE WAVEFORM DATA ===" << "regionId:" << regionId << "audioFilePath:" << audioFilePath;

    // Check if already calculating or calculated
    if (m_waveformCalculationStatus.contains(regionId) && m_waveformCalculationStatus[regionId]) {
        qDebug() << "Already calculating waveform for region:" << regionId;
        return; // Already calculating
    }

    WaveformData *existingData = SharedWaveformCache::instance().getWaveformData(regionId);
    if (existingData && existingData->isComplete) {
        qDebug() << "Waveform already calculated for region:" << regionId;
        return; // Already calculated
    }

    // Check if audio file exists
    if (!QFileInfo::exists(audioFilePath)) {
        qWarning() << "Audio file does not exist for waveform calculation:" << audioFilePath;
        qDebug() << "Creating test waveform for demonstration purposes";

        // Create a test waveform for demonstration when audio file is not found
        QFuture<WaveformData> future = QtConcurrent::run([this, regionId, audioFilePath]() -> WaveformData {
            return calculateWaveformFromFile(audioFilePath); // This will create a test pattern
        });

        // Use QFutureWatcher to handle completion
        QFutureWatcher<WaveformData> *watcher = new QFutureWatcher<WaveformData>(this);
        connect(watcher, &QFutureWatcher<WaveformData>::finished, this, [this, regionId, watcher]() {
            WaveformData result = watcher->result();
            onWaveformCalculationComplete(regionId, result);
            watcher->deleteLater();
        });
        watcher->setFuture(future);
        return;
    }

    qDebug() << "Starting waveform calculation for region:" << regionId << "file:" << audioFilePath;
    m_waveformCalculationStatus[regionId] = true;

    // Use AudioFileManager to get audio data and calculate waveform
    if (m_audioFileManager) {
        // For now, create a simple waveform calculation using QtConcurrent
        QFuture<WaveformData> future = QtConcurrent::run([this, regionId, audioFilePath]() -> WaveformData {
            return calculateWaveformFromFile(audioFilePath);
        });

        // Use QFutureWatcher to handle completion
        QFutureWatcher<WaveformData> *watcher = new QFutureWatcher<WaveformData>(this);
        connect(watcher, &QFutureWatcher<WaveformData>::finished, this, [this, regionId, watcher]() {
            WaveformData result = watcher->result();
            onWaveformCalculationComplete(regionId, result);
            watcher->deleteLater();
        });
        watcher->setFuture(future);
    }
}

void TimelineWidget::onWaveformCalculationComplete(const QString &regionId, const WaveformData &data)
{
    qDebug() << "Waveform calculation completed for region:" << regionId;

    // Store in shared cache
    SharedWaveformCache::instance().insertWaveformData(regionId, data);
    qDebug() << "TimelineWidget::onWaveformCalculationComplete - stored in shared cache on instance" << this
             << "for region" << regionId
             << "shared cache size now:" << SharedWaveformCache::instance().size();

    // Update calculation status
    m_waveformCalculationStatus[regionId] = false;

    // Update display if waveforms are currently shown
    if (m_showWaveforms) {
        for (auto content : m_trackContents) {
            if (content) {
                content->update();
            }
        }
    }
}

WaveformData* TimelineWidget::getWaveformData(const QString &regionId)
{
    WaveformData* result = SharedWaveformCache::instance().getWaveformData(regionId);
    qDebug() << "TimelineWidget::getWaveformData called on instance" << this
             << "for region" << regionId
             << "shared cache size:" << SharedWaveformCache::instance().size()
             << "result:" << (result != nullptr ? "found" : "not found");
    return result;
}

WaveformData TimelineWidget::calculateWaveformFromFile(const QString &audioFilePath)
{
    WaveformData waveformData;

    // This is a simplified waveform calculation
    // In a real implementation, you would use librosa or similar to read audio data
    qDebug() << "Calculating waveform from file:" << audioFilePath;

    try {
        // For now, create a placeholder waveform based on file size and name
        // This should be replaced with actual audio analysis
        QFileInfo fileInfo(audioFilePath);
        qDebug() << "File exists check for:" << audioFilePath << "exists:" << fileInfo.exists();
        if (!fileInfo.exists()) {
            qWarning() << "Audio file does not exist:" << audioFilePath;
            qDebug() << "Creating test waveform anyway for demonstration purposes";
            // Continue to create test waveform even if file doesn't exist
        }

        // Create a realistic test waveform pattern
        int numSamples = 2000; // Number of waveform samples to generate
        waveformData.peaks.resize(numSamples);
        waveformData.rms.resize(numSamples);
        waveformData.duration = 10.0; // Placeholder duration
        waveformData.sampleRate = 44100.0;
        waveformData.samplesPerPixel = 1024;

        // Generate different waveform patterns based on file name to create variety
        QString fileName = QFileInfo(audioFilePath).baseName().toLower();
        bool isDialog = fileName.contains("dialog") || fileName.contains("voice") || fileName.contains("unnamed");
        bool isMusic = fileName.contains("music") || fileName.contains("song");
        bool isSFX = fileName.contains("sfx") || fileName.contains("effect");

        for (int i = 0; i < numSamples; ++i) {
            double t = double(i) / numSamples;
            double amplitude = 0.0;

            if (isDialog) {
                // Dialog pattern: irregular bursts with pauses
                double burst = sin(2.0 * M_PI * t * 8.0) * (sin(2.0 * M_PI * t * 0.7) > 0.3 ? 1.0 : 0.1);
                amplitude = 0.6 * burst * (0.5 + 0.5 * sin(2.0 * M_PI * t * 1.2));
            } else if (isMusic) {
                // Music pattern: more consistent with rhythm
                amplitude = 0.8 * (0.7 + 0.3 * sin(2.0 * M_PI * t * 4.0)) *
                           (0.8 + 0.2 * sin(2.0 * M_PI * t * 0.3));
            } else if (isSFX) {
                // SFX pattern: sharp spikes
                amplitude = 0.9 * abs(sin(2.0 * M_PI * t * 12.0)) *
                           (sin(2.0 * M_PI * t * 2.0) > 0.5 ? 1.0 : 0.2);
            } else {
                // Default pattern: moderate variation
                amplitude = 0.5 * (1.0 + 0.4 * sin(2.0 * M_PI * t * 3.0)) *
                           (0.9 - 0.3 * t) * // Slight fade
                           (0.3 + 0.7 * sin(2.0 * M_PI * t * 0.8));
            }

            // Add some randomness for realism
            amplitude *= (0.9 + 0.2 * QRandomGenerator::global()->generateDouble());
            amplitude = qMax(0.0, qMin(1.0, amplitude)); // Clamp to [0,1]

            waveformData.peaks[i] = amplitude;
            waveformData.rms[i] = amplitude * 0.7; // RMS is typically lower than peak
        }

        waveformData.isComplete = true;
        qDebug() << "Waveform calculation completed for:" << audioFilePath
                 << "peaks size:" << waveformData.peaks.size()
                 << "first few peaks:" << (waveformData.peaks.size() > 0 ? QString::number(waveformData.peaks[0]) : "none")
                 << (waveformData.peaks.size() > 1 ? QString::number(waveformData.peaks[1]) : "")
                 << (waveformData.peaks.size() > 2 ? QString::number(waveformData.peaks[2]) : "");

    } catch (const std::exception &e) {
        qWarning() << "Error calculating waveform:" << e.what();
    }

    return waveformData;
}

// Analysis Range Control Implementation
void TimelineWidget::onAnalysisRangeChanged()
{
    qDebug() << "TimelineWidget::onAnalysisRangeChanged() called";

    // Safety check - ensure all widgets are initialized
    if (!m_analyzeFullAAFCheckBox || !m_startTimeEdit || !m_endTimeEdit ||
        !m_setFromTimelineButton || !m_clearTimeRangeButton) {
        qWarning() << "TimelineWidget::onAnalysisRangeChanged called before widgets are initialized";
        return;
    }

    qDebug() << "TimelineWidget::onAnalysisRangeChanged - widgets are valid";

    bool fullAAF = m_analyzeFullAAFCheckBox->isChecked();
    qDebug() << "TimelineWidget::onAnalysisRangeChanged - fullAAF:" << fullAAF;

    // Enable/disable time input controls
    m_startTimeEdit->setEnabled(!fullAAF);
    m_endTimeEdit->setEnabled(!fullAAF);
    m_setFromTimelineButton->setEnabled(!fullAAF);
    m_clearTimeRangeButton->setEnabled(!fullAAF && (!m_startTimeEdit->text().isEmpty() || !m_endTimeEdit->text().isEmpty()));

    qDebug() << "TimelineWidget::onAnalysisRangeChanged - controls enabled/disabled";

    // Parse time inputs
    double startTime = 0.0;
    double endTime = 0.0;

    if (!fullAAF) {
        QString startTimeText = m_startTimeEdit->text().trimmed();
        QString endTimeText = m_endTimeEdit->text().trimmed();

        qDebug() << "TimelineWidget::onAnalysisRangeChanged - parsing times:" << startTimeText << endTimeText;

        if (!startTimeText.isEmpty()) {
            startTime = TimecodeUtils::parseTimecode(startTimeText);
            qDebug() << "TimelineWidget::onAnalysisRangeChanged - parsed start time:" << startTime;
        }
        if (!endTimeText.isEmpty()) {
            endTime = TimecodeUtils::parseTimecode(endTimeText);
            qDebug() << "TimelineWidget::onAnalysisRangeChanged - parsed end time:" << endTime;
        }

        // Validate time range
        if (endTime > 0 && startTime >= endTime) {
            m_startTimeEdit->setStyleSheet("QLineEdit { background-color: #ffcccc; }");
            m_endTimeEdit->setStyleSheet("QLineEdit { background-color: #ffcccc; }");
        } else {
            m_startTimeEdit->setStyleSheet("");
            m_endTimeEdit->setStyleSheet("");
        }
    }

    qDebug() << "TimelineWidget::onAnalysisRangeChanged - about to emit signal, window():" << (window() != nullptr);

    // Emit signal to notify main window (only if parent window exists)
    if (window()) {
        qDebug() << "TimelineWidget::onAnalysisRangeChanged - emitting analysisRangeChanged signal";
        emit analysisRangeChanged(fullAAF, startTime, endTime);
        qDebug() << "TimelineWidget::onAnalysisRangeChanged - signal emitted successfully";
    }

    qDebug() << "TimelineWidget::onAnalysisRangeChanged - completed";
}

void TimelineWidget::onSetTimeRangeFromTimeline()
{
    // Safety check - ensure all widgets are initialized
    if (!m_analyzeFullAAFCheckBox || !m_startTimeEdit || !m_endTimeEdit) {
        qWarning() << "TimelineWidget::onSetTimeRangeFromTimeline called before widgets are initialized";
        return;
    }

    // Check if we have a time range selection
    if (m_timeRangeSelectionActive && m_timeRangeSelectionStart != m_timeRangeSelectionEnd) {
        double startTime = qMin(m_timeRangeSelectionStart, m_timeRangeSelectionEnd);
        double endTime = qMax(m_timeRangeSelectionStart, m_timeRangeSelectionEnd);

        // Format times as SMPTE timecode
        QString startTimeStr = TimecodeUtils::formatTimecode(startTime, TimecodeUtils::Format::FRAMES, TimecodeUtils::FrameRate::FPS_25);
        QString endTimeStr = TimecodeUtils::formatTimecode(endTime, TimecodeUtils::Format::FRAMES, TimecodeUtils::FrameRate::FPS_25);

        // Uncheck "Analyze Full AAF" and set the time range values
        m_analyzeFullAAFCheckBox->setChecked(false);
        m_startTimeEdit->setText(startTimeStr);
        m_endTimeEdit->setText(endTimeStr);

        // Trigger update
        onAnalysisRangeChanged();
    } else {
        // Emit signal to request timeline range selection (only if parent window exists)
        if (window()) {
            emit setTimeRangeFromTimelineRequested();
        }
    }
}

void TimelineWidget::onClearTimeRange()
{
    // Safety check - ensure all widgets are initialized
    if (!m_startTimeEdit || !m_endTimeEdit) {
        qWarning() << "TimelineWidget::onClearTimeRange called before widgets are initialized";
        return;
    }

    m_startTimeEdit->clear();
    m_endTimeEdit->clear();
    onAnalysisRangeChanged();

    // Emit signal only if parent window exists
    if (window()) {
        emit clearTimeRangeRequested();
    }
}
